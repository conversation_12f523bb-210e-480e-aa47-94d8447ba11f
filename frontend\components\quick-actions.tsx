"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { UploadCloud, FileSearch, Clock } from "lucide-react"
import Link from "next/link"

export default function QuickActions() {
  return (
    <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
      <div className="rounded-lg border border-[#DDD6FE] bg-white p-6 shadow-sm">
        <div className="flex items-center gap-3 mb-3">
          <FileSearch className="h-5 w-5 text-[#003478]" />
          <h3 className="text-[16px] leading-6 font-medium text-[#2D3436]">Search by FINIS Code</h3>
        </div>
        <p className="text-sm text-[#636E72] mb-4">Locate parts instantly using the unique FINIS code.</p>
        <Link href="/catalog">
          <Button
            variant="outline"
            className="border-2 border-[#003478] text-[#003478] hover:bg-[#003478] hover:text-white bg-transparent"
          >
            Start Search
          </Button>
        </Link>
      </div>

      <div className="rounded-lg border border-[#DDD6FE] bg-white p-6 shadow-sm">
        <div className="flex items-center gap-3 mb-3">
          <UploadCloud className="h-5 w-5 text-[#003478]" />
          <h3 className="text-[16px] leading-6 font-medium text-[#2D3436]">Bulk Upload</h3>
        </div>
        <p className="text-sm text-[#636E72] mb-4">Upload CSV to create large orders quickly.</p>
        <Link href="/cart#bulk">
          <Button className="bg-[#003478] hover:bg-[#002456] text-white">Upload CSV</Button>
        </Link>
      </div>

      <div className="rounded-lg border border-[#DDD6FE] bg-white p-6 shadow-sm">
        <div className="flex items-center gap-3 mb-3">
          <Clock className="h-5 w-5 text-[#003478]" />
          <h3 className="text-[16px] leading-6 font-medium text-[#2D3436]">Recent Orders</h3>
        </div>
        <p className="text-sm text-[#636E72] mb-4">Jump back into your recent purchases.</p>
        <Link href="/orders">
          <Button
            variant="outline"
            className="border-2 border-[#003478] text-[#003478] hover:bg-[#003478] hover:text-white bg-transparent"
          >
            View Orders
          </Button>
        </Link>
      </div>
    </div>
  )
}
