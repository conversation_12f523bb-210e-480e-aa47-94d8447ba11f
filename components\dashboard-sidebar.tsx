import Link from "next/link"
import { PackageSearch, Settings, Users, UploadCloud, Tag } from "lucide-react"

const ITEMS = [
  { href: "/dashboard/orders", label: "Order History", icon: PackageSearch },
  { href: "/dashboard/account", label: "Account Settings", icon: Settings },
  { href: "/dashboard/users", label: "User Management", icon: Users },
  { href: "/dashboard/bulk", label: "Bulk Upload", icon: UploadCloud },
  { href: "/promotions", label: "Promotions", icon: Tag },
]

export default function DashboardSidebar() {
  return (
    <nav className="rounded-lg border border-[#DDD6FE] bg-white p-4 shadow-sm">
      <ul className="space-y-1">
        {ITEMS.map((i) => (
          <li key={i.href}>
            <Link
              href={i.href}
              className="flex items-center gap-3 px-2.5 py-2 rounded-md hover:bg-muted focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-[#0066CC]"
            >
              <i.icon className="h-4 w-4 text-[#003478]" />
              <span className="text-sm text-[#2D3436]">{i.label}</span>
            </Link>
          </li>
        ))}
      </ul>
    </nav>
  )
}
