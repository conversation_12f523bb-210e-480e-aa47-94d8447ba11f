"use client"

import type React from "react"

import { useEffect, useMemo, useRef, useState } from "react"
import { Search } from "lucide-react"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import Link from "next/link"

type Suggestion = { id: string; label: string; href: string; meta?: string }

const SAMPLE_SUGGESTIONS: Suggestion[] = [
  { id: "1", label: "Brake Pad Kit - FINIS 1671234", href: "/catalog?finis=1671234", meta: "Front Axle" },
  { id: "2", label: "Oil Filter - FINIS 1234567", href: "/catalog?finis=1234567", meta: "EcoBoost" },
  { id: "3", label: "Air Filter - FINIS 7654321", href: "/catalog?finis=7654321", meta: "Ranger" },
  { id: "4", label: "Spark Plug - FINIS 2222333", href: "/catalog?finis=2222333", meta: "Duratec" },
]

export default function SearchBar({ compact = false }: { compact?: boolean }) {
  const [query, setQuery] = useState("")
  const [open, setOpen] = useState(false)
  const [activeIndex, setActiveIndex] = useState(0)
  const containerRef = useRef<HTMLDivElement>(null)

  const recentKey = "ford:recent-searches"
  const [recent, setRecent] = useState<string[]>([])

  useEffect(() => {
    try {
      const r = JSON.parse(localStorage.getItem(recentKey) || "[]")
      if (Array.isArray(r)) setRecent(r.slice(0, 5))
    } catch {}
  }, [])

  const matches = useMemo(() => {
    if (!query) return []
    const q = query.toLowerCase()
    return SAMPLE_SUGGESTIONS.filter((s) => s.label.toLowerCase().includes(q)).slice(0, 6)
  }, [query])

  useEffect(() => {
    const onDocClick = (e: MouseEvent) => {
      if (!containerRef.current?.contains(e.target as Node)) {
        setOpen(false)
      }
    }
    document.addEventListener("click", onDocClick)
    return () => document.removeEventListener("click", onDocClick)
  }, [])

  const onSubmit = (e?: React.FormEvent) => {
    e?.preventDefault()
    setOpen(false)
    if (query.trim()) {
      const next = [query, ...recent.filter((r) => r !== query)].slice(0, 5)
      setRecent(next)
      localStorage.setItem(recentKey, JSON.stringify(next))
      window.location.href = `/catalog?query=${encodeURIComponent(query)}`
    }
  }

  return (
    <div ref={containerRef} className={cn("relative w-full", compact ? "max-w-full" : "w-[400px]")}>
      <form onSubmit={onSubmit}>
        <div className="relative">
          <Input
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            onFocus={() => setOpen(true)}
            placeholder="Search by FINIS code or part name..."
            className={cn(
              "h-12 pr-11",
              "border-[#DDD6FE] focus-visible:ring-2 focus-visible:ring-[#0066CC] focus-visible:border-[#0066CC]",
            )}
            aria-label="Search"
          />
          <button
            type="submit"
            className="absolute right-1 top-1 h-10 w-10 rounded-md flex items-center justify-center text-white bg-[#003478] hover:bg-[#002456] focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-[#0066CC]"
            aria-label="Submit search"
          >
            <Search className="h-4 w-4" />
          </button>
        </div>
      </form>

      {/* Autocomplete dropdown */}
      {open && (
        <div role="listbox" className="absolute z-40 mt-2 w-full rounded-md border border-[#DDD6FE] bg-white shadow-lg">
          {matches.length > 0 ? (
            <ul>
              {matches.map((s, i) => (
                <li key={s.id}>
                  <Link
                    href={s.href}
                    className={cn(
                      "flex items-center justify-between gap-3 px-3 py-2 text-sm hover:bg-muted focus:bg-muted",
                      i === activeIndex && "bg-muted",
                    )}
                    onMouseEnter={() => setActiveIndex(i)}
                    onClick={() => {
                      const next = [s.label, ...recent.filter((r) => r !== s.label)].slice(0, 5)
                      setRecent(next)
                      localStorage.setItem(recentKey, JSON.stringify(next))
                    }}
                  >
                    <span className="text-[#2D3436]">{s.label}</span>
                    {s.meta ? (
                      <Badge variant="secondary" className="text-[#2D3436]">
                        {s.meta}
                      </Badge>
                    ) : null}
                  </Link>
                </li>
              ))}
            </ul>
          ) : (
            <div className="p-3">
              {query ? (
                <div className="text-sm text-[#636E72]">
                  No results for {'"'}
                  {query}
                  {'"'}. Try a FINIS code or part name.
                </div>
              ) : (
                <div>
                  <div className="text-xs uppercase tracking-wide text-[#636E72] mb-2">Recent</div>
                  <div className="flex flex-wrap gap-2">
                    {recent.length ? (
                      recent.map((r) => (
                        <button
                          key={r}
                          onClick={() => {
                            setQuery(r)
                            setOpen(false)
                            onSubmit()
                          }}
                          className="text-sm px-2.5 py-1.5 rounded border border-[#DDD6FE] hover:bg-muted"
                        >
                          {r}
                        </button>
                      ))
                    ) : (
                      <div className="text-sm text-[#636E72]">No recent searches</div>
                    )}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  )
}
