const express = require("express")
const { body, param, query } = require("express-validator")
const pool = require("../config/database")
const { authenticateToken } = require("../middleware/auth")

const router = express.Router()

// All order routes require authentication
router.use(authenticateToken)

// Get user's orders
router.get(
  "/",
  [
    query("page").optional().isInt({ min: 1 }),
    query("limit").optional().isInt({ min: 1, max: 50 }),
    query("status").optional().isIn(["pending", "processing", "shipped", "delivered", "cancelled"]),
  ],
  async (req, res) => {
    try {
      const { page = 1, limit = 10, status } = req.query
      const offset = (page - 1) * limit

      let whereClause = "WHERE o.user_id = $1"
      const queryParams = [req.user.id]

      if (status) {
        whereClause += " AND o.status = $2"
        queryParams.push(status)
      }

      const ordersQuery = `
      SELECT 
        o.id,
        o.order_number,
        o.status,
        o.subtotal,
        o.tax_amount,
        o.shipping_amount,
        o.total_amount,
        o.created_at,
        o.shipped_at,
        o.delivered_at,
        COUNT(oi.id) as item_count
      FROM orders o
      LEFT JOIN order_items oi ON o.id = oi.order_id
      ${whereClause}
      GROUP BY o.id
      ORDER BY o.created_at DESC
      LIMIT $${queryParams.length + 1} OFFSET $${queryParams.length + 2}
    `

      queryParams.push(limit, offset)
      const result = await pool.query(ordersQuery, queryParams)

      // Get total count
      const countQuery = `SELECT COUNT(*) as total FROM orders o ${whereClause}`
      const countResult = await pool.query(countQuery, queryParams.slice(0, -2))
      const total = Number.parseInt(countResult.rows[0].total)

      res.json({
        orders: result.rows.map((order) => ({
          id: order.id,
          orderNumber: order.order_number,
          status: order.status,
          subtotal: Number.parseFloat(order.subtotal),
          taxAmount: Number.parseFloat(order.tax_amount),
          shippingAmount: Number.parseFloat(order.shipping_amount),
          totalAmount: Number.parseFloat(order.total_amount),
          itemCount: Number.parseInt(order.item_count),
          createdAt: order.created_at,
          shippedAt: order.shipped_at,
          deliveredAt: order.delivered_at,
        })),
        pagination: {
          page: Number.parseInt(page),
          limit: Number.parseInt(limit),
          total,
          totalPages: Math.ceil(total / limit),
        },
      })
    } catch (error) {
      console.error("Orders fetch error:", error)
      res.status(500).json({ error: "Failed to fetch orders" })
    }
  },
)

// Get single order
router.get("/:orderId", [param("orderId").isUUID()], async (req, res) => {
  try {
    const { orderId } = req.params

    // Get order details
    const orderQuery = `
      SELECT * FROM orders 
      WHERE id = $1 AND user_id = $2
    `
    const orderResult = await pool.query(orderQuery, [orderId, req.user.id])

    if (orderResult.rows.length === 0) {
      return res.status(404).json({ error: "Order not found" })
    }

    const order = orderResult.rows[0]

    // Get order items
    const itemsQuery = `
      SELECT 
        oi.*,
        p.image_urls
      FROM order_items oi
      LEFT JOIN products p ON oi.product_id = p.id
      WHERE oi.order_id = $1
      ORDER BY oi.created_at
    `
    const itemsResult = await pool.query(itemsQuery, [orderId])

    res.json({
      id: order.id,
      orderNumber: order.order_number,
      status: order.status,
      subtotal: Number.parseFloat(order.subtotal),
      taxAmount: Number.parseFloat(order.tax_amount),
      shippingAmount: Number.parseFloat(order.shipping_amount),
      totalAmount: Number.parseFloat(order.total_amount),
      shippingAddress: {
        firstName: order.shipping_first_name,
        lastName: order.shipping_last_name,
        company: order.shipping_company,
        addressLine1: order.shipping_address_line1,
        addressLine2: order.shipping_address_line2,
        city: order.shipping_city,
        state: order.shipping_state,
        postalCode: order.shipping_postal_code,
        country: order.shipping_country,
      },
      notes: order.notes,
      trackingNumber: order.tracking_number,
      createdAt: order.created_at,
      shippedAt: order.shipped_at,
      deliveredAt: order.delivered_at,
      items: itemsResult.rows.map((item) => ({
        id: item.id,
        productId: item.product_id,
        finisCode: item.finis_code,
        productName: item.product_name,
        quantity: item.quantity,
        unitPrice: Number.parseFloat(item.unit_price),
        totalPrice: Number.parseFloat(item.total_price),
        imageUrls: item.image_urls,
      })),
    })
  } catch (error) {
    console.error("Order fetch error:", error)
    res.status(500).json({ error: "Failed to fetch order" })
  }
})

// Create new order from cart
router.post(
  "/",
  [
    body("shippingAddress").isObject(),
    body("shippingAddress.firstName").trim().isLength({ min: 1 }),
    body("shippingAddress.lastName").trim().isLength({ min: 1 }),
    body("shippingAddress.addressLine1").trim().isLength({ min: 1 }),
    body("shippingAddress.city").trim().isLength({ min: 1 }),
    body("shippingAddress.state").trim().isLength({ min: 1 }),
    body("shippingAddress.postalCode").trim().isLength({ min: 1 }),
    body("notes").optional().trim(),
  ],
  async (req, res) => {
    const client = await pool.connect()

    try {
      await client.query("BEGIN")

      const { shippingAddress, notes } = req.body

      // Get cart items
      const cartQuery = `
      SELECT 
        ci.product_id,
        ci.quantity,
        ci.unit_price,
        p.name as product_name,
        p.finis_code,
        (ci.quantity * ci.unit_price) as total_price
      FROM cart_items ci
      JOIN carts c ON ci.cart_id = c.id
      JOIN products p ON ci.product_id = p.id
      WHERE c.user_id = $1
    `
      const cartResult = await client.query(cartQuery, [req.user.id])

      if (cartResult.rows.length === 0) {
        await client.query("ROLLBACK")
        return res.status(400).json({ error: "Cart is empty" })
      }

      // Calculate totals
      const subtotal = cartResult.rows.reduce((sum, item) => sum + Number.parseFloat(item.total_price), 0)
      const taxRate = 0.07 // 7% tax
      const taxAmount = subtotal * taxRate
      const shippingAmount = subtotal > 250 ? 0 : 15 // Free shipping over $250
      const totalAmount = subtotal + taxAmount + shippingAmount

      // Generate order number
      const orderNumber = `O-${Date.now()}`

      // Create order
      const orderQuery = `
      INSERT INTO orders (
        order_number, user_id, status, subtotal, tax_amount, shipping_amount, total_amount,
        shipping_first_name, shipping_last_name, shipping_company, shipping_address_line1,
        shipping_address_line2, shipping_city, shipping_state, shipping_postal_code,
        shipping_country, notes
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17)
      RETURNING id
    `

      const orderResult = await client.query(orderQuery, [
        orderNumber,
        req.user.id,
        "pending",
        subtotal,
        taxAmount,
        shippingAmount,
        totalAmount,
        shippingAddress.firstName,
        shippingAddress.lastName,
        shippingAddress.company || null,
        shippingAddress.addressLine1,
        shippingAddress.addressLine2 || null,
        shippingAddress.city,
        shippingAddress.state,
        shippingAddress.postalCode,
        shippingAddress.country || "US",
        notes || null,
      ])

      const orderId = orderResult.rows[0].id

      // Create order items
      for (const item of cartResult.rows) {
        await client.query(
          `
        INSERT INTO order_items (order_id, product_id, finis_code, product_name, quantity, unit_price, total_price)
        VALUES ($1, $2, $3, $4, $5, $6, $7)
      `,
          [
            orderId,
            item.product_id,
            item.finis_code,
            item.product_name,
            item.quantity,
            item.unit_price,
            item.total_price,
          ],
        )

        // Update inventory (reserve items)
        await client.query(
          `
        UPDATE inventory 
        SET quantity_reserved = quantity_reserved + $1
        WHERE product_id = $2
      `,
          [item.quantity, item.product_id],
        )
      }

      // Clear cart
      await client.query(
        `
      DELETE FROM cart_items 
      WHERE cart_id IN (SELECT id FROM carts WHERE user_id = $1)
    `,
        [req.user.id],
      )

      await client.query("COMMIT")

      res.status(201).json({
        message: "Order created successfully",
        orderId,
        orderNumber,
        totalAmount,
      })
    } catch (error) {
      await client.query("ROLLBACK")
      console.error("Order creation error:", error)
      res.status(500).json({ error: "Failed to create order" })
    } finally {
      client.release()
    }
  },
)

module.exports = router
