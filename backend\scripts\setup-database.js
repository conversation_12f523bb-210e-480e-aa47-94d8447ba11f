require('dotenv').config()
const fs = require("fs")
const path = require("path")
const pool = require("../config/database")

async function setupDatabase() {
  try {
    console.log("Setting up Ford B2B Parts database...")

    // Read and execute schema
    const schemaPath = path.join(__dirname, "../database/schema.sql")
    const schema = fs.readFileSync(schemaPath, "utf8")

    await pool.query(schema)
    console.log("✅ Database schema created successfully")

    // Read and execute seed data
    const seedPath = path.join(__dirname, "../database/seed-data.sql")
    const seedData = fs.readFileSync(seedPath, "utf8")

    await pool.query(seedData)
    console.log("✅ Seed data inserted successfully")

    console.log("\n🎉 Database setup complete!")
    console.log("\nDefault users created:")
    console.log("Admin: <EMAIL> / admin123")
    console.log("Manager: <EMAIL> / admin123")
    console.log("User: <EMAIL> / admin123")

    process.exit(0)
  } catch (error) {
    console.error("❌ Database setup failed:", error)
    process.exit(1)
  }
}

setupDatabase()
