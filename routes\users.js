const express = require("express")
const { body, param, query } = require("express-validator")
const pool = require("../config/database")
const { authenticateToken, requireRole } = require("../middleware/auth")

const router = express.Router()

// All user routes require authentication
router.use(authenticateToken)

// Get all users (admin/manager only)
router.get(
  "/",
  requireRole(["admin", "manager"]),
  [
    query("page").optional().isInt({ min: 1 }),
    query("limit").optional().isInt({ min: 1, max: 100 }),
    query("role").optional().isIn(["admin", "manager", "user"]),
    query("active").optional().isBoolean(),
  ],
  async (req, res) => {
    try {
      const { page = 1, limit = 20, role, active } = req.query
      const offset = (page - 1) * limit

      const whereConditions = []
      const queryParams = []
      let paramCount = 0

      if (role) {
        paramCount++
        whereConditions.push(`role = $${paramCount}`)
        queryParams.push(role)
      }

      if (active !== undefined) {
        paramCount++
        whereConditions.push(`is_active = $${paramCount}`)
        queryParams.push(active === "true")
      }

      // If user is manager, only show users from same dealership
      if (req.user.role === "manager") {
        paramCount++
        whereConditions.push(`dealership_code = $${paramCount}`)
        queryParams.push(req.user.dealership_code)
      }

      const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(" AND ")}` : ""

      // Get total count
      const countQuery = `SELECT COUNT(*) as total FROM users ${whereClause}`
      const countResult = await pool.query(countQuery, queryParams)
      const total = Number.parseInt(countResult.rows[0].total)

      // Get users
      paramCount++
      queryParams.push(limit)
      paramCount++
      queryParams.push(offset)

      const usersQuery = `
      SELECT id, email, first_name, last_name, role, dealership_name, 
             dealership_code, phone, is_active, created_at
      FROM users
      ${whereClause}
      ORDER BY created_at DESC
      LIMIT $${paramCount - 1} OFFSET $${paramCount}
    `

      const usersResult = await pool.query(usersQuery, queryParams)

      res.json({
        users: usersResult.rows.map((user) => ({
          id: user.id,
          email: user.email,
          firstName: user.first_name,
          lastName: user.last_name,
          role: user.role,
          dealershipName: user.dealership_name,
          dealershipCode: user.dealership_code,
          phone: user.phone,
          isActive: user.is_active,
          createdAt: user.created_at,
        })),
        pagination: {
          page: Number.parseInt(page),
          limit: Number.parseInt(limit),
          total,
          totalPages: Math.ceil(total / limit),
        },
      })
    } catch (error) {
      console.error("Users fetch error:", error)
      res.status(500).json({ error: "Failed to fetch users" })
    }
  },
)

// Update user (admin only, or user updating themselves)
router.put(
  "/:userId",
  [
    param("userId").isUUID(),
    body("firstName").optional().trim().isLength({ min: 1 }),
    body("lastName").optional().trim().isLength({ min: 1 }),
    body("phone").optional().trim(),
    body("role").optional().isIn(["admin", "manager", "user"]),
    body("isActive").optional().isBoolean(),
  ],
  async (req, res) => {
    try {
      const { userId } = req.params
      const { firstName, lastName, phone, role, isActive } = req.body

      // Check permissions
      if (userId !== req.user.id && !["admin", "manager"].includes(req.user.role)) {
        return res.status(403).json({ error: "Insufficient permissions" })
      }

      // Only admins can change roles and active status
      if ((role || isActive !== undefined) && req.user.role !== "admin") {
        return res.status(403).json({ error: "Only admins can change role or active status" })
      }

      // Build update query
      const updates = []
      const values = []
      let paramCount = 0

      if (firstName) {
        paramCount++
        updates.push(`first_name = $${paramCount}`)
        values.push(firstName)
      }

      if (lastName) {
        paramCount++
        updates.push(`last_name = $${paramCount}`)
        values.push(lastName)
      }

      if (phone !== undefined) {
        paramCount++
        updates.push(`phone = $${paramCount}`)
        values.push(phone)
      }

      if (role && req.user.role === "admin") {
        paramCount++
        updates.push(`role = $${paramCount}`)
        values.push(role)
      }

      if (isActive !== undefined && req.user.role === "admin") {
        paramCount++
        updates.push(`is_active = $${paramCount}`)
        values.push(isActive)
      }

      if (updates.length === 0) {
        return res.status(400).json({ error: "No valid fields to update" })
      }

      paramCount++
      values.push(userId)

      const updateQuery = `
      UPDATE users 
      SET ${updates.join(", ")}, updated_at = CURRENT_TIMESTAMP
      WHERE id = $${paramCount}
      RETURNING id, email, first_name, last_name, role, is_active
    `

      const result = await pool.query(updateQuery, values)

      if (result.rows.length === 0) {
        return res.status(404).json({ error: "User not found" })
      }

      const user = result.rows[0]
      res.json({
        message: "User updated successfully",
        user: {
          id: user.id,
          email: user.email,
          firstName: user.first_name,
          lastName: user.last_name,
          role: user.role,
          isActive: user.is_active,
        },
      })
    } catch (error) {
      console.error("User update error:", error)
      res.status(500).json({ error: "Failed to update user" })
    }
  },
)

module.exports = router
