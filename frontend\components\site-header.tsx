"use client"

import Link from "next/link"
import { useEffect, useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Sheet, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, SheetTrigger } from "@/components/ui/sheet"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { ShoppingCart, User, Menu, LogOut, Package, Home, Info, Phone } from "lucide-react"
import { cn } from "@/lib/utils"
import SearchBar from "./site-search-bar"

const NAV_LINKS = [
  { href: "/", label: "Home", icon: Home },
  { href: "/catalog", label: "Shop", icon: Package },
  { href: "/about", label: "About", icon: Info },
  { href: "/contact", label: "Contact", icon: Phone },
  { href: "/promotions", label: "Promotions", icon: Package },
]

export default function SiteHeader() {
  const [isScrolled, setIsScrolled] = useState(false)
  const [cartCount, setCartCount] = useState(2)

  useEffect(() => {
    const onScroll = () => setIsScrolled(window.scrollY > 8)
    onScroll()
    window.addEventListener("scroll", onScroll)
    return () => window.removeEventListener("scroll", onScroll)
  }, [])

  return (
    <header
      className={cn("sticky top-0 z-50 w-full border-b", "bg-white", isScrolled ? "shadow-sm" : "shadow-none")}
      role="banner"
      aria-label="Primary"
    >
      {/* Top row */}
      <div className="w-full">
        <div className="mx-auto max-w-[1200px] px-4 md:px-6 h-16 flex items-center gap-4">
          <Link href="/" className="flex items-center gap-2" aria-label="Ford Parts Platform - Home">
            {/* Replace with official Ford logo asset */}
            <div className="flex items-center justify-center rounded-full bg-[#003478] text-white w-9 h-9 font-bold">
              F
            </div>
            <span className="font-semibold text-[#2D3436]">Ford Parts Platform</span>
          </Link>

          <div className="hidden md:flex flex-1 justify-center">
            <SearchBar />
          </div>

          <div className="ml-auto flex items-center gap-2">
            <Link href="/cart" className="relative" aria-label="Cart">
              <Button variant="ghost" className="relative h-10 w-10 p-0 hover:bg-[#E6EEF7]">
                <ShoppingCart className="h-5 w-5 text-[#003478]" />
                <span className="sr-only">{"Open cart"}</span>
                <span className="absolute -top-1 -right-1 rounded-full bg-[#003478] text-white text-[10px] leading-none px-1.5 py-1">
                  {cartCount}
                </span>
              </Button>
            </Link>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="h-10 gap-2 border-[#DDD6FE] text-[#2D3436] bg-transparent">
                  <User className="h-4 w-4 text-[#003478]" />
                  <span className="hidden sm:inline">Profile</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuLabel>Account</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link href="/dashboard">Dashboard</Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/account">Account Settings</Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/orders">Order History</Link>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem>
                  <LogOut className="mr-2 h-4 w-4" />
                  <span>Sign out</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            <Sheet>
              <SheetTrigger asChild>
                <Button variant="ghost" className="h-10 w-10 p-0 md:hidden" aria-label="Open menu">
                  <Menu className="h-5 w-5 text-[#003478]" />
                </Button>
              </SheetTrigger>
              <SheetContent side="left" className="p-0">
                <SheetHeader className="px-4 py-3 border-b">
                  <SheetTitle className="flex items-center gap-2">
                    <div className="flex items-center justify-center rounded-full bg-[#003478] text-white w-8 h-8 font-bold">
                      F
                    </div>
                    <span>Ford Parts Platform</span>
                  </SheetTitle>
                </SheetHeader>
                <nav className="p-2">
                  {NAV_LINKS.map((l) => (
                    <Link
                      key={l.href}
                      href={l.href}
                      className="flex items-center gap-3 px-3 py-2.5 rounded-md hover:bg-muted"
                    >
                      <l.icon className="h-4 w-4 text-[#003478]" />
                      <span className="text-[15px]">{l.label}</span>
                    </Link>
                  ))}
                  <div className="p-3">
                    <SearchBar compact />
                  </div>
                </nav>
              </SheetContent>
            </Sheet>
          </div>
        </div>

        {/* Bottom nav row */}
        <div className="w-full border-t">
          <nav
            className="mx-auto max-w-[1200px] px-4 md:px-6 h-11 hidden md:flex items-center gap-6"
            aria-label="Secondary"
          >
            {NAV_LINKS.map((l) => (
              <Link
                key={l.href}
                href={l.href}
                className="text-sm text-[#2D3436] hover:text-[#003478] focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-[#0066CC] rounded"
              >
                {l.label}
              </Link>
            ))}
          </nav>
        </div>
      </div>
    </header>
  )
}
