const express = require("express")
const multer = require("multer")
const csv = require("csv-parser")
const fs = require("fs")
const path = require("path")
const pool = require("../config/database")
const { authenticateToken } = require("../middleware/auth")

const router = express.Router()

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = process.env.UPLOAD_DIR || "uploads"
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true })
    }
    cb(null, uploadDir)
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + "-" + Math.round(Math.random() * 1e9)
    cb(null, `bulk-upload-${uniqueSuffix}.csv`)
  },
})

const upload = multer({
  storage,
  limits: {
    fileSize: Number.parseInt(process.env.MAX_FILE_SIZE) || 10 * 1024 * 1024, // 10MB
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype === "text/csv" || file.originalname.endsWith(".csv")) {
      cb(null, true)
    } else {
      cb(new Error("Only CSV files are allowed"))
    }
  },
})

// All upload routes require authentication
router.use(authenticateToken)

// Bulk upload CSV
router.post("/bulk", upload.single("csvFile"), async (req, res) => {
  if (!req.file) {
    return res.status(400).json({ error: "No CSV file uploaded" })
  }

  const client = await pool.connect()

  try {
    await client.query("BEGIN")

    // Create bulk upload record
    const uploadResult = await client.query(
      `
      INSERT INTO bulk_uploads (user_id, filename, total_rows, status)
      VALUES ($1, $2, 0, 'processing')
      RETURNING id
    `,
      [req.user.id, req.file.originalname],
    )

    const uploadId = uploadResult.rows[0].id
    const results = []
    const errors = []
    let totalRows = 0
    let successfulRows = 0

    // Get or create user's cart
    const cartResult = await client.query("SELECT id FROM carts WHERE user_id = $1", [req.user.id])

    let cartId
    if (cartResult.rows.length === 0) {
      const newCartResult = await client.query("INSERT INTO carts (user_id) VALUES ($1) RETURNING id", [req.user.id])
      cartId = newCartResult.rows[0].id
    } else {
      cartId = cartResult.rows[0].id
    }

    // Process CSV file
    await new Promise((resolve, reject) => {
      fs.createReadStream(req.file.path)
        .pipe(csv())
        .on("data", async (row) => {
          totalRows++

          try {
            const finisCode = row.finis_code || row.FINIS || row.finis || row["FINIS Code"]
            const quantity = Number.parseInt(row.quantity || row.qty || row.Quantity || "1")

            if (!finisCode) {
              errors.push({ row: totalRows, error: "Missing FINIS code" })
              return
            }

            if (!quantity || quantity < 1) {
              errors.push({ row: totalRows, error: "Invalid quantity" })
              return
            }

            // Find product by FINIS code (including mappings)
            let productResult = await client.query(
              `
              SELECT id, name, price FROM products 
              WHERE finis_code = $1 AND is_active = true
            `,
              [finisCode],
            )

            // If not found, check FINIS mappings
            if (productResult.rows.length === 0) {
              productResult = await client.query(
                `
                SELECT p.id, p.name, p.price 
                FROM products p
                JOIN finis_mappings fm ON p.finis_code = fm.new_finis
                WHERE fm.old_finis = $1 AND p.is_active = true
              `,
                [finisCode],
              )
            }

            if (productResult.rows.length === 0) {
              errors.push({ row: totalRows, error: `Product not found for FINIS code: ${finisCode}` })
              return
            }

            const product = productResult.rows[0]

            // Check if item already exists in cart
            const existingItemResult = await client.query(
              "SELECT id, quantity FROM cart_items WHERE cart_id = $1 AND product_id = $2",
              [cartId, product.id],
            )

            if (existingItemResult.rows.length > 0) {
              // Update existing item
              const newQuantity = existingItemResult.rows[0].quantity + quantity
              await client.query("UPDATE cart_items SET quantity = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2", [
                newQuantity,
                existingItemResult.rows[0].id,
              ])
            } else {
              // Add new item
              await client.query(
                "INSERT INTO cart_items (cart_id, product_id, quantity, unit_price) VALUES ($1, $2, $3, $4)",
                [cartId, product.id, quantity, product.price],
              )
            }

            results.push({
              finisCode,
              productName: product.name,
              quantity,
              unitPrice: Number.parseFloat(product.price),
            })

            successfulRows++
          } catch (error) {
            errors.push({ row: totalRows, error: error.message })
          }
        })
        .on("end", resolve)
        .on("error", reject)
    })

    // Update bulk upload record
    await client.query(
      `
      UPDATE bulk_uploads 
      SET total_rows = $1, successful_rows = $2, failed_rows = $3, 
          status = $4, error_log = $5, completed_at = CURRENT_TIMESTAMP
      WHERE id = $6
    `,
      [
        totalRows,
        successfulRows,
        totalRows - successfulRows,
        errors.length === 0 ? "completed" : "completed",
        JSON.stringify(errors),
        uploadId,
      ],
    )

    await client.query("COMMIT")

    // Clean up uploaded file
    fs.unlinkSync(req.file.path)

    res.json({
      message: "Bulk upload completed",
      uploadId,
      summary: {
        totalRows,
        successfulRows,
        failedRows: totalRows - successfulRows,
        itemsAdded: results.length,
      },
      results: results.slice(0, 10), // Return first 10 results
      errors: errors.slice(0, 10), // Return first 10 errors
    })
  } catch (error) {
    await client.query("ROLLBACK")
    console.error("Bulk upload error:", error)

    // Clean up uploaded file
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path)
    }

    res.status(500).json({ error: "Bulk upload failed" })
  } finally {
    client.release()
  }
})

// Get bulk upload history
router.get("/history", async (req, res) => {
  try {
    const result = await pool.query(
      `
      SELECT id, filename, total_rows, successful_rows, failed_rows, 
             status, created_at, completed_at
      FROM bulk_uploads
      WHERE user_id = $1
      ORDER BY created_at DESC
      LIMIT 20
    `,
      [req.user.id],
    )

    res.json(
      result.rows.map((upload) => ({
        id: upload.id,
        filename: upload.filename,
        totalRows: upload.total_rows,
        successfulRows: upload.successful_rows,
        failedRows: upload.failed_rows,
        status: upload.status,
        createdAt: upload.created_at,
        completedAt: upload.completed_at,
      })),
    )
  } catch (error) {
    console.error("Upload history error:", error)
    res.status(500).json({ error: "Failed to fetch upload history" })
  }
})

// Download sample CSV template
router.get("/template", (req, res) => {
  const csvContent = `finis_code,quantity
1671234,2
1234567,1
7654321,3
2222333,4`

  res.setHeader("Content-Type", "text/csv")
  res.setHeader("Content-Disposition", 'attachment; filename="bulk-upload-template.csv"')
  res.send(csvContent)
})

module.exports = router
