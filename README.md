# Ford B2B Parts Platform - Backend API

A complete Express.js backend API for the Ford B2B Parts Platform with PostgreSQL database.

## Features

- **Authentication & Authorization**: JWT-based auth with role-based access control
- **Product Management**: Full CRUD operations with search, filtering, and FINIS code mapping
- **Shopping Cart**: Persistent cart with bulk upload functionality
- **Order Management**: Complete order lifecycle management
- **Inventory Tracking**: Real-time inventory with availability status
- **Search & Suggestions**: Full-text search with autocomplete suggestions
- **Bulk Operations**: CSV upload and processing for large orders
- **Security**: Rate limiting, input validation, SQL injection protection

## Database Schema

The PostgreSQL database includes these main tables:

- `users` - User authentication and profiles
- `products` - Product catalog with FINIS codes
- `categories` - Product categorization
- `brands` - Brand management (Ford, Motorcraft, etc.)
- `inventory` - Stock levels and availability
- `finis_mappings` - Old to new FINIS code mappings
- `carts` & `cart_items` - Shopping cart functionality
- `orders` & `order_items` - Order management
- `promotions` - Discount and promotion system

## Quick Start

1. **Install Dependencies**
   \`\`\`bash
   npm install
   \`\`\`

2. **Setup Environment**
   \`\`\`bash
   cp .env.example .env
   # Edit .env with your database credentials
   \`\`\`

3. **Setup Database**
   \`\`\`bash
   npm run setup-db
   \`\`\`

4. **Start Development Server**
   \`\`\`bash
   npm run dev
   \`\`\`

The API will be available at `http://localhost:3001`

## API Endpoints

### Authentication
- `POST /api/auth/register` - Register new user
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `GET /api/auth/profile` - Get user profile

### Products
- `GET /api/products` - Get products with filtering/search
- `GET /api/products/:id` - Get single product
- `GET /api/products/finis/:code` - Get product by FINIS code
- `GET /api/products/meta/categories` - Get categories
- `GET /api/products/meta/brands` - Get brands

### Search
- `GET /api/search/suggestions?q=term` - Get search suggestions
- `POST /api/search/log` - Log search queries

### Cart (Protected Routes)
- `GET /api/cart` - Get user's cart
- `POST /api/cart/items` - Add item to cart
- `PUT /api/cart/items/:id` - Update cart item
- `DELETE /api/cart/items/:id` - Remove cart item

### Orders (Protected Routes)
- `GET /api/orders` - Get user's orders
- `POST /api/orders` - Create new order
- `GET /api/orders/:id` - Get single order

## Database Setup

The database schema supports:

- **FINIS Code Mapping**: Handles old to new part number transitions
- **Inventory Management**: Real-time stock tracking
- **Product Compatibility**: Vehicle fitment information
- **Bulk Operations**: CSV upload processing
- **Search Optimization**: Full-text search indexes
- **Audit Trails**: Created/updated timestamps

## Security Features

- JWT token authentication with database session tracking
- Password hashing with bcrypt
- Rate limiting to prevent abuse
- Input validation and sanitization
- SQL injection protection
- CORS configuration
- Helmet.js security headers

## Sample Data

The seed script creates:
- Sample product categories (Brakes, Filters, Engine, etc.)
- Ford and Motorcraft brands
- Sample products with FINIS codes
- Inventory records
- FINIS code mappings
- Sample promotions
- Test user accounts

## Production Deployment

1. Set `NODE_ENV=production`
2. Use strong JWT secret
3. Configure SSL for database connection
4. Set up proper CORS origins
5. Configure email service for notifications
6. Set up monitoring and logging

## Environment Variables

See `.env.example` for all required environment variables.

## Contributing

1. Follow the existing code structure
2. Add proper error handling
3. Include input validation
4. Write meaningful commit messages
5. Test all endpoints before submitting
