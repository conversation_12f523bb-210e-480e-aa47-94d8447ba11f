# Ford B2B Parts Platform

A complete B2B e-commerce platform for Ford dealerships and service centers.

## Project Structure

This project consists of two separate applications:

- **Backend**: Express.js API with PostgreSQL database (port 3001)
- **Frontend**: Next.js application with shadcn/ui components (port 3000)

## Quick Start

### 1. <PERSON><PERSON> and setup the project:
\`\`\`bash
# Create project directory
mkdir ford-b2b-parts-platform
cd ford-b2b-parts-platform

# Copy files to respective folders
# - Copy all files with "frontend/" prefix to frontend/ folder
# - Copy all files with "backend/" prefix to backend/ folder
\`\`\`

### 2. Setup Backend:
\`\`\`bash
cd backend
npm install
cp .env.example .env
# Edit .env with your database credentials
npm run setup-db
npm run dev
\`\`\`

### 3. Setup Frontend:
\`\`\`bash
cd ../frontend
npm install
npm run dev
\`\`\`

### 4. Access the application:
- Frontend: http://localhost:3000
- Backend API: http://localhost:3001

## File Structure

\`\`\`
ford-b2b-parts-platform/
├── README.md
├── backend/
│   ├── package.json
│   ├── server.js
│   ├── .env.example
│   ├── .env
│   ├── config/
│   │   └── database.js
│   ├── middleware/
│   │   └── auth.js
│   ├── routes/
│   │   ├── auth.js
│   │   ├── products.js
│   │   ├── cart.js
│   │   ├── orders.js
│   │   ├── users.js
│   │   ├── upload.js
│   │   └── search.js
│   ├── database/
│   │   ├── schema.sql
│   │   └── seed-data.sql
│   └── scripts/
│       └── setup-database.js
└── frontend/
    ├── package.json
    ├── next.config.js
    ├── tailwind.config.ts
    ├── .env.local
    ├── app/
    │   ├── layout.tsx
    │   ├── page.tsx
    │   ├── globals.css
    │   ├── catalog/
    │   │   └── page.tsx
    │   ├── cart/
    │   │   └── page.tsx
    │   ├── dashboard/
    │   │   └── page.tsx
    │   ├── about/
    │   │   └── page.tsx
    │   ├── contact/
    │   │   └── page.tsx
    │   └── promotions/
    │       └── page.tsx
    ├── components/
    │   ├── ui/
    │   ├── site-header.tsx
    │   ├── site-footer.tsx
    │   ├── site-search-bar.tsx
    │   ├── product-card.tsx
    │   ├── quick-actions.tsx
    │   ├── news-updates.tsx
    │   ├── bulk-upload.tsx
    │   └── dashboard-sidebar.tsx
    ├── lib/
    │   ├── utils.ts
    │   └── api.ts
    └── public/
        └── images/
\`\`\`

## Environment Variables

### Backend (.env)
\`\`\`
DB_HOST=localhost
DB_PORT=5432
DB_NAME=ford_b2b_parts
DB_USER=postgres
DB_PASSWORD=your_password
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
PORT=3001
NODE_ENV=development
FRONTEND_URL=http://localhost:3000
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760
\`\`\`

### Frontend (.env.local)
\`\`\`
NEXT_PUBLIC_API_URL=http://localhost:3001/api
\`\`\`

## Default Test Users

- **Admin**: <EMAIL> / admin123
- **Manager**: <EMAIL> / admin123  
- **User**: <EMAIL> / admin123

## Database Schema

The database includes:
- Products with FINIS codes
- Categories and brands
- User authentication
- Shopping cart functionality
- Order management
- Bulk upload history
- Inventory tracking

## API Endpoints

- `GET /api/products` - Product catalog
- `POST /api/auth/login` - User authentication
- `GET /api/cart` - Shopping cart
- `POST /api/orders` - Create orders
- `POST /api/upload/bulk` - Bulk CSV upload

## Features

- ✅ User Authentication & Authorization
- ✅ Product Catalog with FINIS Code Search
- ✅ Shopping Cart & Checkout
- ✅ Order Management
- ✅ Bulk CSV Upload
- ✅ Inventory Management
- ✅ FINIS Code Mapping (Old → New)
- ✅ Role-based Access Control
- ✅ Responsive Design

## Development Workflow

Run both servers simultaneously:

**Terminal 1 (Backend):**
\`\`\`bash
cd backend
npm run dev
\`\`\`

**Terminal 2 (Frontend):**
\`\`\`bash
cd frontend
npm run dev
\`\`\`

## Troubleshooting

1. **Database connection issues**: Check PostgreSQL is running and credentials in .env
2. **CORS errors**: Ensure FRONTEND_URL in backend .env matches frontend URL
3. **Port conflicts**: Change PORT in .env files if needed
4. **Missing dependencies**: Run `npm install` in both directories

## Next Steps

1. Replace placeholder images with official Ford assets
2. Configure production database
3. Set up SSL certificates
4. Configure email service for notifications
5. Add monitoring and logging
