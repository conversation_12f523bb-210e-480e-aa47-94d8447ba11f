const express = require("express")
const { query, param } = require("express-validator")
const pool = require("../config/database")

const router = express.Router()

// Get all products with filtering, search, and pagination
router.get(
  "/",
  [
    query("page").optional().isInt({ min: 1 }),
    query("limit").optional().isInt({ min: 1, max: 100 }),
    query("search").optional().trim(),
    query("category").optional().isUUID(),
    query("brand").optional().isUUID(),
    query("minPrice").optional().isFloat({ min: 0 }),
    query("maxPrice").optional().isFloat({ min: 0 }),
    query("inStock").optional().isBoolean(),
    query("featured").optional().isBoolean(),
  ],
  async (req, res) => {
    try {
      const { page = 1, limit = 12, search, category, brand, minPrice, maxPrice, inStock, featured } = req.query

      const offset = (page - 1) * limit
      const whereConditions = ["p.is_active = true"]
      const queryParams = []
      let paramCount = 0

      // Build WHERE conditions
      if (search) {
        paramCount++
        whereConditions.push(`(
        p.name ILIKE $${paramCount} OR 
        p.finis_code ILIKE $${paramCount} OR 
        p.part_number ILIKE $${paramCount} OR
        p.description ILIKE $${paramCount}
      )`)
        queryParams.push(`%${search}%`)
      }

      if (category) {
        paramCount++
        whereConditions.push(`p.category_id = $${paramCount}`)
        queryParams.push(category)
      }

      if (brand) {
        paramCount++
        whereConditions.push(`p.brand_id = $${paramCount}`)
        queryParams.push(brand)
      }

      if (minPrice) {
        paramCount++
        whereConditions.push(`p.price >= $${paramCount}`)
        queryParams.push(minPrice)
      }

      if (maxPrice) {
        paramCount++
        whereConditions.push(`p.price <= $${paramCount}`)
        queryParams.push(maxPrice)
      }

      if (inStock === "true") {
        whereConditions.push("i.quantity_available > 0")
      } else if (inStock === "false") {
        whereConditions.push("i.quantity_available <= 0")
      }

      if (featured === "true") {
        whereConditions.push("p.is_featured = true")
      }

      const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(" AND ")}` : ""

      // Get total count
      const countQuery = `
      SELECT COUNT(DISTINCT p.id) as total
      FROM products p
      LEFT JOIN inventory i ON p.id = i.product_id
      LEFT JOIN categories c ON p.category_id = c.id
      LEFT JOIN brands b ON p.brand_id = b.id
      ${whereClause}
    `

      const countResult = await pool.query(countQuery, queryParams)
      const total = Number.parseInt(countResult.rows[0].total)

      // Get products
      paramCount++
      queryParams.push(limit)
      paramCount++
      queryParams.push(offset)

      const productsQuery = `
      SELECT 
        p.id,
        p.name,
        p.description,
        p.finis_code,
        p.part_number,
        p.price,
        p.image_urls,
        p.is_featured,
        c.name as category_name,
        c.slug as category_slug,
        b.name as brand_name,
        b.slug as brand_slug,
        COALESCE(i.quantity_available, 0) as quantity_available,
        CASE WHEN i.quantity_available > 0 THEN true ELSE false END as in_stock
      FROM products p
      LEFT JOIN inventory i ON p.id = i.product_id
      LEFT JOIN categories c ON p.category_id = c.id
      LEFT JOIN brands b ON p.brand_id = b.id
      ${whereClause}
      ORDER BY p.is_featured DESC, p.name ASC
      LIMIT $${paramCount - 1} OFFSET $${paramCount}
    `

      const productsResult = await pool.query(productsQuery, queryParams)

      res.json({
        products: productsResult.rows,
        pagination: {
          page: Number.parseInt(page),
          limit: Number.parseInt(limit),
          total,
          totalPages: Math.ceil(total / limit),
        },
      })
    } catch (error) {
      console.error("Products fetch error:", error)
      res.status(500).json({ error: "Failed to fetch products" })
    }
  },
)

// Get single product by ID
router.get("/:id", [param("id").isUUID()], async (req, res) => {
  try {
    const { id } = req.params

    const query = `
      SELECT 
        p.*,
        c.name as category_name,
        c.slug as category_slug,
        b.name as brand_name,
        b.slug as brand_slug,
        COALESCE(i.quantity_available, 0) as quantity_available,
        CASE WHEN i.quantity_available > 0 THEN true ELSE false END as in_stock
      FROM products p
      LEFT JOIN inventory i ON p.id = i.product_id
      LEFT JOIN categories c ON p.category_id = c.id
      LEFT JOIN brands b ON p.brand_id = b.id
      WHERE p.id = $1 AND p.is_active = true
    `

    const result = await pool.query(query, [id])

    if (result.rows.length === 0) {
      return res.status(404).json({ error: "Product not found" })
    }

    // Get compatibility information
    const compatibilityQuery = `
      SELECT vehicle_year, vehicle_make, vehicle_model, vehicle_trim, engine_type, notes
      FROM product_compatibility
      WHERE product_id = $1
      ORDER BY vehicle_year DESC, vehicle_make, vehicle_model
    `

    const compatibilityResult = await pool.query(compatibilityQuery, [id])

    const product = {
      ...result.rows[0],
      compatibility: compatibilityResult.rows,
    }

    res.json(product)
  } catch (error) {
    console.error("Product fetch error:", error)
    res.status(500).json({ error: "Failed to fetch product" })
  }
})

// Get product by FINIS code (including old FINIS mappings)
router.get("/finis/:finisCode", [param("finisCode").trim().isLength({ min: 1 })], async (req, res) => {
  try {
    const { finisCode } = req.params

    // First try direct match
    const query = `
      SELECT 
        p.*,
        c.name as category_name,
        c.slug as category_slug,
        b.name as brand_name,
        b.slug as brand_slug,
        COALESCE(i.quantity_available, 0) as quantity_available,
        CASE WHEN i.quantity_available > 0 THEN true ELSE false END as in_stock
      FROM products p
      LEFT JOIN inventory i ON p.id = i.product_id
      LEFT JOIN categories c ON p.category_id = c.id
      LEFT JOIN brands b ON p.brand_id = b.id
      WHERE p.finis_code = $1 AND p.is_active = true
    `

    let result = await pool.query(query, [finisCode])

    // If not found, check FINIS mappings
    if (result.rows.length === 0) {
      const mappingQuery = `
        SELECT 
          p.*,
          c.name as category_name,
          c.slug as category_slug,
          b.name as brand_name,
          b.slug as brand_slug,
          COALESCE(i.quantity_available, 0) as quantity_available,
          CASE WHEN i.quantity_available > 0 THEN true ELSE false END as in_stock,
          fm.mapping_type,
          fm.notes as mapping_notes
        FROM finis_mappings fm
        JOIN products p ON fm.new_finis = p.finis_code
        LEFT JOIN inventory i ON p.id = i.product_id
        LEFT JOIN categories c ON p.category_id = c.id
        LEFT JOIN brands b ON p.brand_id = b.id
        WHERE fm.old_finis = $1 AND p.is_active = true
      `

      result = await pool.query(mappingQuery, [finisCode])

      if (result.rows.length === 0) {
        return res.status(404).json({ error: "Product not found for FINIS code" })
      }

      // Add mapping information to response
      const product = result.rows[0]
      product.finis_mapping = {
        old_finis: finisCode,
        new_finis: product.finis_code,
        mapping_type: product.mapping_type,
        notes: product.mapping_notes,
      }

      return res.json(product)
    }

    res.json(result.rows[0])
  } catch (error) {
    console.error("FINIS lookup error:", error)
    res.status(500).json({ error: "Failed to lookup FINIS code" })
  }
})

// Get categories
router.get("/meta/categories", async (req, res) => {
  try {
    const query = `
      SELECT id, name, slug, description, parent_id, sort_order
      FROM categories
      WHERE is_active = true
      ORDER BY sort_order, name
    `

    const result = await pool.query(query)
    res.json(result.rows)
  } catch (error) {
    console.error("Categories fetch error:", error)
    res.status(500).json({ error: "Failed to fetch categories" })
  }
})

// Get brands
router.get("/meta/brands", async (req, res) => {
  try {
    const query = `
      SELECT id, name, slug, logo_url
      FROM brands
      WHERE is_active = true
      ORDER BY name
    `

    const result = await pool.query(query)
    res.json(result.rows)
  } catch (error) {
    console.error("Brands fetch error:", error)
    res.status(500).json({ error: "Failed to fetch brands" })
  }
})

module.exports = router
