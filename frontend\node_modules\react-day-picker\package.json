{"name": "react-day-picker", "version": "9.8.0", "description": "Customizable Date Picker for React", "author": "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "homepage": "https://daypicker.dev", "packageManager": "pnpm@9.15.9", "engines": {"node": ">=18"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/gpbl/react-day-picker"}, "bugs": {"url": "https://github.com/gpbl/react-day-picker/issues"}, "main": "./dist/cjs/index.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/esm/index.js", "style": "./src/style.css", "type": "module", "sideEffects": ["**/*.css"], "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}, "./jalali": {"import": {"types": "./dist/esm/jalali.d.ts", "default": "./dist/esm/jalali.js"}, "require": {"types": "./dist/cjs/jalali.d.ts", "default": "./dist/cjs/jalali.js"}}, "./persian": {"import": {"types": "./dist/esm/persian.d.ts", "default": "./dist/esm/persian.js"}, "require": {"types": "./dist/cjs/persian.d.ts", "default": "./dist/cjs/persian.js"}}, "./locale": {"import": {"types": "./dist/esm/locale.d.ts", "default": "./dist/esm/locale.js"}, "require": {"types": "./dist/cjs/locale.d.ts", "default": "./dist/cjs/locale.js"}}, "./style": {"sass": "./src/style.css"}, "./style.css": {"style": {"default": "./src/style.css"}, "import": {"types": "./src/style.module.css.d.ts", "default": "./src/style.css"}, "require": {"types": "./src/style.module.css.d.ts", "default": "./src/style.css"}}, "./style.module": {"sass": "./src/style.module.css"}, "./style.module.css": {"style": {"default": "./src/style.module.css"}, "import": {"types": "./src/style.module.css.d.ts", "default": "./src/style.module.css"}, "require": {"types": "./src/style.module.css.d.ts", "default": "./src/style.module.css"}}, "./src/style.css": {"style": {"default": "./src/style.css"}, "import": {"types": "./src/style.module.css.d.ts", "default": "./src/style.css"}, "require": {"types": "./src/style.module.css.d.ts", "default": "./src/style.css"}}, "./src/style.module.css": {"style": {"default": "./src/style.module.css"}, "import": {"types": "./src/style.module.css.d.ts", "default": "./src/style.module.css"}, "require": {"types": "./src/style.module.css.d.ts", "default": "./src/style.module.css"}}, "./dist/style.css": {"style": {"default": "./src/style.css"}, "import": {"types": "./src/style.module.css.d.ts", "default": "./src/style.css"}, "require": {"types": "./src/style.module.css.d.ts", "default": "./src/style.css"}}, "./dist/style.module.css": {"style": {"default": "./src/style.module.css"}, "import": {"types": "./src/style.module.css.d.ts", "default": "./src/style.module.css"}, "require": {"types": "./src/style.module.css.d.ts", "default": "./src/style.module.css"}}, "./package.json": {"import": "./package.json", "require": "./package.json", "default": "./package.json"}, "./examples": {"types": "./examples/index.d.ts", "import": "./examples/index.ts"}}, "scripts": {"prepublish": "pnpm build", "build": "pnpm build:cjs && pnpm build:esm && pnpm build:css", "build:cjs": "tsc --project tsconfig-cjs.json && echo '{ \"type\": \"commonjs\" }' > dist/cjs/package.json", "build:esm": "tsc --project tsconfig-esm.json", "build:css": "./scripts/build-css.sh ./src/style.css ./src/style.module.css", "lint": "eslint .", "test": "jest --selectProjects examples --selectProjects src", "test:build": "jest --selectProjects examples/built", "test-watch": "jest --watch", "typecheck": "tsc --project ./tsconfig.json --noEmit", "typecheck-watch": "tsc --project ./tsconfig.json --noEmit --watch"}, "files": ["dist", "src", "jalali.js", "jalali.d.ts", "persian.js", "persian.d.ts", "locale.js", "locale.d.ts"], "dependencies": {"@date-fns/tz": "1.2.0", "date-fns": "4.1.0", "date-fns-jalali": "4.1.0-0"}, "devDependencies": {"@jest/types": "^29.6.3", "@radix-ui/react-select": "^2.1.7", "@swc/core": "^1.11.8", "@swc/jest": "^0.2.37", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/jest": "^29.5.14", "@types/node": "^22.14.1", "@types/react": "^19.1.1", "@types/react-dom": "^19.1.2", "@typescript-eslint/eslint-plugin": "^8.29.1", "@typescript-eslint/parser": "^8.29.1", "eslint": "^8.57.1", "eslint-config-prettier": "^10.1.2", "eslint-import-resolver-typescript": "^3.10.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jest": "^28.11.0", "eslint-plugin-prettier": "^5.2.6", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-require-extensions": "^0.1.3", "eslint-plugin-testing-library": "^7.1.1", "html-validate": "^9.5.2", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-transform-css": "^6.0.2", "mockdate": "^3.0.5", "prettier": "^3.5.3", "prettier-plugin-jsdoc": "^1.3.2", "react": "^19.1.0", "react-dom": "^19.1.0", "ts-node": "^10.9.2", "tslib": "^2.8.1", "typescript": "~5.8.3", "typescript-css-modules": "^1.0.4"}, "peerDependencies": {"react": ">=16.8.0"}, "funding": {"type": "individual", "url": "https://github.com/sponsors/gpbl"}}