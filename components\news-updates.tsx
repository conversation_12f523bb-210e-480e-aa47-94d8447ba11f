export default function NewsUpdates() {
  const news = [
    {
      id: "n1",
      title: "Q3 Genuine Parts Pricing Update",
      date: "2025-08-01",
      excerpt:
        "Pricing adjustments for selected models go into effect September 1. Review the changelog and download the full list.",
    },
    {
      id: "n2",
      title: "New EcoBoost Service Kits",
      date: "2025-07-15",
      excerpt: "EcoBoost engines now have consolidated service kits to streamline ordering and reduce errors.",
    },
    {
      id: "n3",
      title: "Holiday Shipping Schedules",
      date: "2025-07-01",
      excerpt: "Plan ahead with our updated shipping cutoffs and warehouse schedules for the holiday period.",
    },
    {
      id: "n4",
      title: "Warehouse Maintenance Notice",
      date: "2025-06-21",
      excerpt: "Brief maintenance window scheduled this weekend. Some orders may experience delays.",
    },
  ]
  return (
    <section aria-labelledby="updates-heading">
      <h2 id="updates-heading" className="text-[24px] leading-8 font-semibold text-[#2D3436] mb-4">
        Company News & Updates
      </h2>
      <div className="grid gap-4 md:grid-cols-2">
        {news.map((n) => (
          <article key={n.id} className="rounded-lg border border-[#DDD6FE] bg-white p-5 shadow-sm">
            <h3 className="text-[16px] leading-6 font-medium text-[#2D3436]">{n.title}</h3>
            <div className="text-xs text-[#636E72] mt-1">{new Date(n.date).toLocaleDateString()}</div>
            <p className="text-sm text-[#636E72] mt-2">{n.excerpt}</p>
            <button className="mt-3 text-sm text-[#003478] hover:underline">Read more</button>
          </article>
        ))}
      </div>
    </section>
  )
}
