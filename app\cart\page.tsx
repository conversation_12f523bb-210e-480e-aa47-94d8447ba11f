"use client"

import { useMemo, useState } from "react"
import SiteHeader from "@/components/site-header"
import SiteFooter from "@/components/site-footer"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Separator } from "@/components/ui/separator"
import { Trash2 } from "lucide-react"
import BulkUpload from "@/components/bulk-upload"

type CartItem = {
  id: string
  name: string
  finis: string
  price: number
  qty: number
}

const INITIAL: CartItem[] = [
  { id: "c1", name: "Brake Pad Kit", finis: "1671234", price: 129.99, qty: 2 },
  { id: "c2", name: "Oil Filter", finis: "1234567", price: 19.99, qty: 4 },
  { id: "c3", name: "Air Filter", finis: "7654321", price: 24.5, qty: 1 },
]

export default function CartPage() {
  const [items, setItems] = useState<CartItem[]>(INITIAL)

  const summary = useMemo(() => {
    const subtotal = items.reduce((sum, i) => sum + i.price * i.qty, 0)
    const tax = subtotal * 0.07
    const shipping = subtotal > 250 ? 0 : 15
    const total = subtotal + tax + shipping
    return { subtotal, tax, shipping, total }
  }, [items])

  return (
    <div className="bg-white">
      <SiteHeader />
      <main className="mx-auto max-w-[1200px] px-4 md:px-6 py-6 grid gap-6 lg:grid-cols-[1fr_360px]" role="main">
        {/* Cart items */}
        <section aria-labelledby="cart-heading" className="rounded-lg border border-[#DDD6FE] bg-white p-4 shadow-sm">
          <h1 id="cart-heading" className="text-[24px] leading-8 font-semibold text-[#2D3436] mb-3">
            Shopping Cart
          </h1>
          <div className="space-y-4">
            {items.map((i, idx) => (
              <div key={i.id} className="grid grid-cols-[1fr_auto] gap-4 items-center">
                <div>
                  <div className="font-medium text-[#2D3436]">{i.name}</div>
                  <div className="text-sm text-[#003478]">FINIS {i.finis}</div>
                  <div className="text-sm text-[#636E72]">${i.price.toFixed(2)} each</div>
                </div>
                <div className="flex items-center gap-2">
                  <label className="sr-only" htmlFor={`qty-${i.id}`}>
                    Quantity
                  </label>
                  <div className="flex items-center">
                    <button
                      className="h-9 w-9 border border-[#DDD6FE] rounded-l hover:bg-muted"
                      onClick={() =>
                        setItems((prev) => prev.map((p, k) => (k === idx ? { ...p, qty: Math.max(1, p.qty - 1) } : p)))
                      }
                      aria-label="Decrease quantity"
                    >
                      {"-"}
                    </button>
                    <Input
                      id={`qty-${i.id}`}
                      className="h-9 w-14 rounded-none text-center border-y border-[#DDD6FE]"
                      value={i.qty}
                      onChange={(e) => {
                        const val = Number.parseInt(e.target.value || "1", 10)
                        setItems((prev) =>
                          prev.map((p, k) => (k === idx ? { ...p, qty: Math.max(1, isNaN(val) ? 1 : val) } : p)),
                        )
                      }}
                      inputMode="numeric"
                    />
                    <button
                      className="h-9 w-9 border border-[#DDD6FE] rounded-r hover:bg-muted"
                      onClick={() => setItems((prev) => prev.map((p, k) => (k === idx ? { ...p, qty: p.qty + 1 } : p)))}
                      aria-label="Increase quantity"
                    >
                      {"+"}
                    </button>
                  </div>
                  <Button
                    variant="ghost"
                    className="text-[#D63031] hover:text-[#D63031]"
                    onClick={() => setItems((prev) => prev.filter((p) => p.id !== i.id))}
                    aria-label={`Remove ${i.name}`}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
                <Separator className="col-span-2" />
              </div>
            ))}
          </div>

          {/* Bulk Upload Section */}
          <div id="bulk" className="mt-6">
            <BulkUpload />
          </div>
        </section>

        {/* Order summary */}
        <aside
          aria-labelledby="summary-heading"
          className="rounded-lg border border-[#DDD6FE] bg-white p-4 shadow-sm h-fit"
        >
          <h2 id="summary-heading" className="text-[20px] leading-7 font-semibold text-[#2D3436] mb-2">
            Order Summary
          </h2>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-[#636E72]">Subtotal</span>
              <span className="text-[#2D3436]">${summary.subtotal.toFixed(2)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-[#636E72]">Tax (7%)</span>
              <span className="text-[#2D3436]">${summary.tax.toFixed(2)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-[#636E72]">Shipping</span>
              <span className="text-[#2D3436]">${summary.shipping.toFixed(2)}</span>
            </div>
            <Separator />
            <div className="flex justify-between text-[16px] leading-6 font-medium">
              <span>Total</span>
              <span>${summary.total.toFixed(2)}</span>
            </div>
          </div>
          <Button className="mt-4 w-full bg-[#003478] hover:bg-[#002456] text-white">Proceed to Checkout</Button>
        </aside>
      </main>
      <SiteFooter />
    </div>
  )
}
