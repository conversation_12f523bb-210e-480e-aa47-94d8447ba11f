import SiteHeader from "@/components/site-header"
import SiteFooter from "@/components/site-footer"
import DashboardSidebar from "@/components/dashboard-sidebar"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

const STATS = [
  { label: "Open Orders", value: 8 },
  { label: "Backordered Items", value: 3 },
  { label: "Spend (30d)", value: "$12,430" },
  { label: "Users", value: 14 },
]

const RECENT = [
  { id: "O-10234", date: "2025-08-03", items: 12, total: 1299.99, status: "Shipped" },
  { id: "O-10233", date: "2025-08-02", items: 4, total: 235.1, status: "Processing" },
  { id: "O-10232", date: "2025-08-01", items: 7, total: 732.5, status: "Delivered" },
  { id: "O-10231", date: "2025-07-29", items: 2, total: 88.0, status: "Cancelled" },
]

export default function DashboardPage() {
  return (
    <div className="bg-white">
      <SiteHeader />
      <main className="mx-auto max-w-[1200px] px-4 md:px-6 py-6 grid gap-6 md:grid-cols-[260px_1fr]" role="main">
        <aside className="md:sticky md:top-20 h-fit">
          <DashboardSidebar />
        </aside>

        <section className="grid gap-6">
          {/* Stats */}
          <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
            {STATS.map((s) => (
              <Card key={s.label} className="border-[#DDD6FE]">
                <CardHeader className="py-3">
                  <CardTitle className="text-sm text-[#636E72]">{s.label}</CardTitle>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="text-[24px] leading-8 font-semibold text-[#2D3436]">{s.value}</div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Recent orders table */}
          <div className="rounded-lg border border-[#DDD6FE] bg-white shadow-sm overflow-hidden">
            <div className="px-4 py-3">
              <h2 className="text-[20px] leading-7 font-semibold text-[#2D3436]">Recent Orders</h2>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full text-sm">
                <thead className="bg-muted">
                  <tr className="text-left">
                    <th className="px-4 py-2 font-medium">Order ID</th>
                    <th className="px-4 py-2 font-medium">Date</th>
                    <th className="px-4 py-2 font-medium">Items</th>
                    <th className="px-4 py-2 font-medium">Total</th>
                    <th className="px-4 py-2 font-medium">Status</th>
                  </tr>
                </thead>
                <tbody>
                  {RECENT.map((r) => (
                    <tr key={r.id} className="border-t">
                      <td className="px-4 py-2">{r.id}</td>
                      <td className="px-4 py-2">{new Date(r.date).toLocaleDateString()}</td>
                      <td className="px-4 py-2">{r.items}</td>
                      <td className="px-4 py-2">${r.total.toFixed(2)}</td>
                      <td className="px-4 py-2">
                        <Badge variant="secondary" className="text-[#2D3436]">
                          {r.status}
                        </Badge>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Quick actions */}
          <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
            <a href="/catalog" className="rounded-lg border border-[#DDD6FE] p-5 hover:bg-muted">
              <div className="text-[16px] leading-6 font-medium text-[#2D3436]">Create New Order</div>
              <div className="text-sm text-[#636E72]">Browse catalog and add items to cart.</div>
            </a>
            <a href="/cart#bulk" className="rounded-lg border border-[#DDD6FE] p-5 hover:bg-muted">
              <div className="text-[16px] leading-6 font-medium text-[#2D3436]">Bulk Upload</div>
              <div className="text-sm text-[#636E72]">Upload a CSV file with FINIS codes and quantities.</div>
            </a>
            <a href="/dashboard/users" className="rounded-lg border border-[#DDD6FE] p-5 hover:bg-muted">
              <div className="text-[16px] leading-6 font-medium text-[#2D3436]">Invite User</div>
              <div className="text-sm text-[#636E72]">Add staff to manage orders and approvals.</div>
            </a>
          </div>
        </section>
      </main>
      <SiteFooter />
    </div>
  )
}
