{"version": 3, "sources": ["../../src/trace/trace-uploader.ts"], "names": ["EVENT_FILTER", "Set", "NEXT_TRACE_UPLOAD_DEBUG", "NEXT_TRACE_UPLOAD_FULL", "process", "env", "isDebugEnabled", "shouldUploadFullTrace", "traceUploadUrl", "mode", "projectDir", "distDir", "argv", "upload", "nextVersion", "JSON", "parse", "fsPromise", "readFile", "path", "resolve", "__dirname", "version", "telemetry", "Telemetry", "projectPkgJsonPath", "findUp", "assert", "projectPkgJson", "pkgName", "name", "commit", "child_process", "spawnSync", "os", "platform", "shell", "stdout", "toString", "trimEnd", "readLineInterface", "createInterface", "input", "createReadStream", "join", "crlfDelay", "Infinity", "isTurboSession", "traces", "Map", "line", "lineEvents", "event", "parentId", "undefined", "has", "trace", "get", "traceId", "set", "tags", "isTurbopack", "push", "body", "metadata", "anonymousId", "arch", "cpus", "length", "sessionId", "values", "console", "log", "stringify", "res", "fetch", "method", "headers", "status", "json"], "mappings": ";;;;+DAAmB;iEACG;sEACI;+DACP;kEACD;2DACH;0BACiB;oBACC;6DAChB;yBACS;;;;;;AAE1B,iEAAiE;AACjE,yEAAyE;AACzE,yCAAyC;AACzC,MAAMA,eAAe,IAAIC,IAAI;IAC3B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,EACJC,uBAAuB,EACvB,uFAAuF;AACvF,sGAAsG;AACtG,mFAAmF;AACnFC,sBAAsB,EACvB,GAAGC,QAAQC,GAAG;AAEf,MAAMC,iBAAiB,CAAC,CAACJ,2BAA2B,CAAC,CAACC;AACtD,MAAMI,wBAAwB,CAAC,CAACJ;AAEhC,MAAM,KAAKK,gBAAgBC,MAAMC,YAAYC,QAAQ,GAAGP,QAAQQ,IAAI;AA+BlE,CAAA,eAAeC;IACf,MAAMC,cAAcC,KAAKC,KAAK,CAC5B,MAAMC,iBAAS,CAACC,QAAQ,CACtBC,aAAI,CAACC,OAAO,CAACC,WAAW,uBACxB,SAEFC,OAAO;IAET,MAAMC,YAAY,IAAIC,kBAAS,CAAC;QAAEb;IAAQ;IAE1C,MAAMc,qBAAqB,MAAMC,IAAAA,eAAM,EAAC;IACxCC,IAAAA,eAAM,EAACF;IAEP,MAAMG,iBAAiBb,KAAKC,KAAK,CAC/B,MAAMC,iBAAS,CAACC,QAAQ,CAACO,oBAAoB;IAE/C,MAAMI,UAAUD,eAAeE,IAAI;IAEnC,MAAMC,SAASC,sBAAa,CACzBC,SAAS,CACRC,WAAE,CAACC,QAAQ,OAAO,UAAU,YAAY,OACxC;QAAC;QAAa;KAAO,EACrB;QAAEC,OAAO;IAAK,GAEfC,MAAM,CAACC,QAAQ,GACfC,OAAO;IAEV,MAAMC,oBAAoBC,IAAAA,yBAAe,EAAC;QACxCC,OAAOC,IAAAA,oBAAgB,EAACxB,aAAI,CAACyB,IAAI,CAAClC,YAAYC,SAAS;QACvDkC,WAAWC;IACb;IAEA,IAAIC,iBAAiB;IACrB,MAAMC,SAAS,IAAIC;IACnB,WAAW,MAAMC,QAAQV,kBAAmB;QAC1C,MAAMW,aAA2BpC,KAAKC,KAAK,CAACkC;QAC5C,KAAK,MAAME,SAASD,WAAY;YAC9B,IACE,4BAA4B;YAC5BC,MAAMC,QAAQ,KAAKC,aACnB/C,yBACAP,aAAauD,GAAG,CAACH,MAAMtB,IAAI,GAC3B;gBACA,IAAI0B,QAAQR,OAAOS,GAAG,CAACL,MAAMM,OAAO;gBACpC,IAAIF,UAAUF,WAAW;oBACvBE,QAAQ,EAAE;oBACVR,OAAOW,GAAG,CAACP,MAAMM,OAAO,EAAEF;gBAC5B;gBACA,IAAI,OAAOJ,MAAMQ,IAAI,CAACC,WAAW,KAAK,WAAW;oBAC/Cd,iBAAiBK,MAAMQ,IAAI,CAACC,WAAW;gBACzC;gBACAL,MAAMM,IAAI,CAACV;YACb;QACF;IACF;IAEA,MAAMW,OAAyB;QAC7BC,UAAU;YACRC,aAAa1C,UAAU0C,WAAW;YAClCC,MAAMhC,WAAE,CAACgC,IAAI;YACbnC;YACAoC,MAAMjC,WAAE,CAACiC,IAAI,GAAGC,MAAM;YACtBrB;YACAtC;YACAK;YACAe;YACAM,UAAUD,WAAE,CAACC,QAAQ;YACrBkC,WAAW9C,UAAU8C,SAAS;QAChC;QACArB,QAAQ;eAAIA,OAAOsB,MAAM;SAAG;IAC9B;IAEA,IAAIhE,gBAAgB;QAClBiE,QAAQC,GAAG,CAAC,6BAA6BzD,KAAK0D,SAAS,CAACV,MAAM,MAAM;IACtE;IAEA,IAAIW,MAAM,MAAMC,IAAAA,kBAAK,EAACnE,gBAAgB;QACpCoE,QAAQ;QACRC,SAAS;YACP,gBAAgB;YAChB,yBAAyBtE,wBAAwB,SAAS;QAC5D;QACAwD,MAAMhD,KAAK0D,SAAS,CAACV;IACvB;IAEA,IAAIzD,gBAAgB;QAClBiE,QAAQC,GAAG,CAAC,qBAAqBE,IAAII,MAAM,EAAE,MAAMJ,IAAIK,IAAI;IAC7D;AACF,CAAA"}