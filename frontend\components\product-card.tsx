"use client"

import Image from "next/image"
import { Button } from "@/components/ui/button"
import { CheckCircle2, CircleAlert } from "lucide-react"
import { cn } from "@/lib/utils"

export type Product = {
  id: string
  name: string
  finis: string
  price: number
  image?: string
  available?: boolean
  brand?: string
}

export default function ProductCard({ product }: { product: Product }) {
  const { id, name, finis, price, image, available = true } = product
  return (
    <div
      className={cn(
        "group rounded-lg border border-[#DDD6FE] bg-white shadow-sm hover:shadow-md transition-shadow",
        "focus-within:ring-2 focus-within:ring-[#0066CC]",
      )}
      role="article"
      aria-labelledby={`product-${id}-title`}
    >
      <div className="relative w-full overflow-hidden rounded-t-lg">
        <Image
          src={image || "/placeholder.svg?height=180&width=240&query=ford%20auto%20part%20product"}
          alt={`${name} image`}
          width={240}
          height={180}
          className="w-full aspect-[4/3] object-cover"
        />
      </div>
      <div className="p-4 grid gap-2">
        <div id={`product-${id}-title`} className="text-[16px] leading-6 font-medium text-[#2D3436] line-clamp-2">
          {name}
        </div>
        <div className="text-[14px] leading-5 text-[#003478] font-semibold">FINIS {finis}</div>
        <div className="flex items-center justify-between">
          <div className="text-[18px] leading-6 font-bold text-[#2D3436]">${price.toFixed(2)}</div>
          <div className="flex items-center gap-1 text-sm">
            {available ? (
              <>
                <CheckCircle2 className="h-4 w-4 text-[#00B894]" aria-hidden="true" />
                <span className="text-[#00B894]">In stock</span>
              </>
            ) : (
              <>
                <CircleAlert className="h-4 w-4 text-[#E17000]" aria-hidden="true" />
                <span className="text-[#E17000]">Backorder</span>
              </>
            )}
          </div>
        </div>
        <Button className="mt-2 w-full bg-[#003478] hover:bg-[#002456] text-white" aria-label={`Add ${name} to cart`}>
          Add to Cart
        </Button>
      </div>
    </div>
  )
}
