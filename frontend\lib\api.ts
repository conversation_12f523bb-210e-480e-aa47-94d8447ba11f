// API client for connecting to Express backend

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:3001/api"

class ApiClient {
  private baseURL: string
  private token: string | null = null

  constructor(baseURL: string) {
    this.baseURL = baseURL

    // Get token from localStorage on client side
    if (typeof window !== "undefined") {
      this.token = localStorage.getItem("auth_token")
    }
  }

  setToken(token: string) {
    this.token = token
    if (typeof window !== "undefined") {
      localStorage.setItem("auth_token", token)
    }
  }

  clearToken() {
    this.token = null
    if (typeof window !== "undefined") {
      localStorage.removeItem("auth_token")
    }
  }

  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const url = `${this.baseURL}${endpoint}`

    const headers: HeadersInit = {
      "Content-Type": "application/json",
      ...options.headers,
    }

    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`
    }

    const response = await fetch(url, {
      ...options,
      headers,
    })

    if (!response.ok) {
      const error = await response.json().catch(() => ({ error: "Network error" }))
      throw new Error(error.error || `HTTP ${response.status}`)
    }

    return response.json()
  }

  // Auth methods
  async login(email: string, password: string) {
    const response = await this.request<{
      user: any
      token: string
      message: string
    }>("/auth/login", {
      method: "POST",
      body: JSON.stringify({ email, password }),
    })

    this.setToken(response.token)
    return response
  }

  async register(userData: {
    email: string
    password: string
    firstName: string
    lastName: string
    dealershipName?: string
    dealershipCode?: string
    phone?: string
  }) {
    const response = await this.request<{
      user: any
      token: string
      message: string
    }>("/auth/register", {
      method: "POST",
      body: JSON.stringify(userData),
    })

    this.setToken(response.token)
    return response
  }

  async logout() {
    await this.request("/auth/logout", { method: "POST" })
    this.clearToken()
  }

  async getProfile() {
    return this.request<any>("/auth/profile")
  }

  // Products methods
  async getProducts(
    params: {
      page?: number
      limit?: number
      search?: string
      category?: string
      brand?: string
      minPrice?: number
      maxPrice?: number
      inStock?: boolean
      featured?: boolean
    } = {},
  ) {
    const searchParams = new URLSearchParams()

    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        searchParams.append(key, value.toString())
      }
    })

    return this.request<{
      products: any[]
      pagination: {
        page: number
        limit: number
        total: number
        totalPages: number
      }
    }>(`/products?${searchParams}`)
  }

  async getProduct(id: string) {
    return this.request<any>(`/products/${id}`)
  }

  async getProductByFinis(finisCode: string) {
    return this.request<any>(`/products/finis/${finisCode}`)
  }

  async getCategories() {
    return this.request<any[]>("/products/meta/categories")
  }

  async getBrands() {
    return this.request<any[]>("/products/meta/brands")
  }

  // Search methods
  async getSearchSuggestions(query: string) {
    return this.request<any[]>(`/search/suggestions?q=${encodeURIComponent(query)}`)
  }

  // Cart methods
  async getCart() {
    return this.request<{
      items: any[]
      total: number
    }>("/cart")
  }

  async addToCart(productId: string, quantity: number) {
    return this.request<any>("/cart/items", {
      method: "POST",
      body: JSON.stringify({ productId, quantity }),
    })
  }

  async updateCartItem(itemId: string, quantity: number) {
    return this.request<any>(`/cart/items/${itemId}`, {
      method: "PUT",
      body: JSON.stringify({ quantity }),
    })
  }

  async removeFromCart(itemId: string) {
    return this.request<any>(`/cart/items/${itemId}`, {
      method: "DELETE",
    })
  }

  async clearCart() {
    return this.request<any>("/cart", {
      method: "DELETE",
    })
  }

  // Orders methods
  async getOrders(
    params: {
      page?: number
      limit?: number
      status?: string
    } = {},
  ) {
    const searchParams = new URLSearchParams()

    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        searchParams.append(key, value.toString())
      }
    })

    return this.request<{
      orders: any[]
      pagination: any
    }>(`/orders?${searchParams}`)
  }

  async getOrder(orderId: string) {
    return this.request<any>(`/orders/${orderId}`)
  }

  async createOrder(orderData: {
    shippingAddress: {
      firstName: string
      lastName: string
      company?: string
      addressLine1: string
      addressLine2?: string
      city: string
      state: string
      postalCode: string
      country?: string
    }
    notes?: string
  }) {
    return this.request<{
      orderId: string
      orderNumber: string
      totalAmount: number
      message: string
    }>("/orders", {
      method: "POST",
      body: JSON.stringify(orderData),
    })
  }

  // Upload methods
  async bulkUpload(file: File) {
    const formData = new FormData()
    formData.append("csvFile", file)

    const headers: HeadersInit = {}
    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`
    }

    const response = await fetch(`${this.baseURL}/upload/bulk`, {
      method: "POST",
      headers,
      body: formData,
    })

    if (!response.ok) {
      const error = await response.json().catch(() => ({ error: "Upload failed" }))
      throw new Error(error.error || `HTTP ${response.status}`)
    }

    return response.json()
  }

  async getUploadHistory() {
    return this.request<any[]>("/upload/history")
  }

  async downloadTemplate() {
    const response = await fetch(`${this.baseURL}/upload/template`)
    return response.blob()
  }
}

export const apiClient = new ApiClient(API_BASE_URL)
export default apiClient
