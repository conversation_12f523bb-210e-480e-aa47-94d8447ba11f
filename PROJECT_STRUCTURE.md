# Ford B2B Parts Platform - Project Structure

This project consists of two separate applications:

## Frontend (Next.js)
\`\`\`
frontend/
├── package.json          # Next.js dependencies
├── next.config.js
├── tailwind.config.ts
├── app/
│   ├── layout.tsx
│   ├── page.tsx
│   ├── catalog/
│   ├── cart/
│   └── dashboard/
├── components/
│   ├── ui/               # shadcn/ui components
│   ├── site-header.tsx
│   ├── site-footer.tsx
│   └── ...
└── lib/
    └── utils.ts
\`\`\`

## Backend (Express API)
\`\`\`
backend/
├── package.json          # Express dependencies
├── server.js
├── .env
├── config/
│   └── database.js
├── routes/
│   ├── auth.js
│   ├── products.js
│   ├── cart.js
│   ├── orders.js
│   ├── users.js
│   ├── upload.js
│   └── search.js
├── middleware/
│   └── auth.js
├── database/
│   ├── schema.sql
│   └── seed-data.sql
└── scripts/
    └── setup-database.js
\`\`\`

## Setup Instructions

1. **Backend Setup:**
   \`\`\`bash
   mkdir ford-b2b-backend
   cd ford-b2b-backend
   # Copy all backend files here
   npm install
   npm run setup-db
   npm run dev
   \`\`\`

2. **Frontend Setup:**
   \`\`\`bash
   mkdir ford-b2b-frontend  
   cd ford-b2b-frontend
   # Copy all frontend files here
   npm install
   npm run dev
   \`\`\`

The backend runs on port 3001, frontend on port 3000.
