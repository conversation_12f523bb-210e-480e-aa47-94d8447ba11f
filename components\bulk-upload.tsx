"use client"

import { useEffect, useRef, useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Download, Upload } from "lucide-react"
import { cn } from "@/lib/utils"

export default function BulkUpload() {
  const [dragOver, setDragOver] = useState(false)
  const [fileName, setFileName] = useState<string | null>(null)
  const [progress, setProgress] = useState(0)
  const [status, setStatus] = useState<"idle" | "uploading" | "success" | "error">("idle")
  const inputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    let t: any
    if (status === "uploading") {
      t = setInterval(() => setProgress((p) => Math.min(100, p + 7)), 200)
    }
    if (progress >= 100 && status === "uploading") {
      setStatus("success")
      clearInterval(t)
    }
    return () => clearInterval(t)
  }, [status, progress])

  const onFiles = (file: File) => {
    setFileName(file.name)
    setProgress(0)
    setStatus("uploading")
    // Simulate parsing; add validations here
    setTimeout(() => {
      setProgress(100)
    }, 1800)
  }

  return (
    <div className="rounded-lg border border-[#DDD6FE] bg-white p-5 shadow-sm">
      <div className="flex items-center justify-between gap-3 mb-3">
        <h3 className="text-[16px] leading-6 font-medium text-[#2D3436]">Bulk Upload</h3>
        <button
          onClick={() => alert("Sample CSV would download in production.")}
          className="inline-flex items-center gap-2 text-sm text-[#003478] hover:underline"
        >
          <Download className="h-4 w-4" />
          Sample CSV
        </button>
      </div>
      <div
        onDragOver={(e) => {
          e.preventDefault()
          setDragOver(true)
        }}
        onDragLeave={() => setDragOver(false)}
        onDrop={(e) => {
          e.preventDefault()
          setDragOver(false)
          const f = e.dataTransfer.files?.[0]
          if (f) onFiles(f)
        }}
        className={cn(
          "border-2 border-dashed rounded-md p-6 text-center transition-colors",
          dragOver ? "border-[#0066CC] bg-[#F1F7FF]" : "border-[#DDD6FE]",
        )}
        role="region"
        aria-label="Bulk upload dropzone"
      >
        <Upload className="h-6 w-6 mx-auto text-[#003478] mb-2" />
        <p className="text-sm text-[#636E72] mb-3">Drag and drop your CSV here, or</p>
        <Button
          type="button"
          variant="outline"
          className="border-2 border-[#003478] text-[#003478] hover:bg-[#003478] hover:text-white bg-transparent"
          onClick={() => inputRef.current?.click()}
        >
          Browse files
        </Button>
        <input
          ref={inputRef}
          type="file"
          accept=".csv"
          className="hidden"
          onChange={(e) => {
            const f = e.target.files?.[0]
            if (f) onFiles(f)
          }}
        />
      </div>

      {fileName && (
        <div className="mt-4">
          <div className="flex items-center justify-between text-sm mb-2">
            <span className="text-[#2D3436] truncate">{fileName}</span>
            <span className="text-[#636E72]">{progress}%</span>
          </div>
          <Progress value={progress} className="h-2" />
          <div className="mt-2 text-sm">
            {status === "success" && <span className="text-[#00B894]">Upload complete. Items added to cart.</span>}
            {status === "error" && <span className="text-[#D63031]">Upload failed. Please try again.</span>}
            {status === "uploading" && <span className="text-[#636E72]">Uploading...</span>}
          </div>
        </div>
      )}
    </div>
  )
}
