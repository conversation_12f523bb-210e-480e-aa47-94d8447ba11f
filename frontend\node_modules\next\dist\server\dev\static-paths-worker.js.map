{"version": 3, "sources": ["../../../src/server/dev/static-paths-worker.ts"], "names": ["loadStaticPaths", "dir", "distDir", "pathname", "config", "httpAgentOptions", "locales", "defaultLocale", "isAppPath", "page", "isrFlushToDisk", "fetchCacheKeyPrefix", "maxMemoryCacheSize", "requestHeaders", "cache<PERSON><PERSON><PERSON>", "ppr", "require", "setConfig", "setHttpClientAndAgentOptions", "components", "loadComponents", "isDev", "getStaticPaths", "Error", "routeModule", "generateParams", "isAppRouteRouteModule", "revalidate", "userland", "dynamic", "dynamicParams", "generateStaticParams", "segmentPath", "collectGenerateParams", "ComponentMod", "tree", "buildAppStaticPaths", "configFileName", "buildStaticPaths"], "mappings": ";;;;+BAyBsBA;;;eAAAA;;;QAvBf;QACA;uBAMA;gCAEwB;mCACc;wBAEP;AAW/B,eAAeA,gBAAgB,EACpCC,GAAG,EACHC,OAAO,EACPC,QAAQ,EACRC,MAAM,EACNC,gBAAgB,EAChBC,OAAO,EACPC,aAAa,EACbC,SAAS,EACTC,IAAI,EACJC,cAAc,EACdC,mBAAmB,EACnBC,kBAAkB,EAClBC,cAAc,EACdC,YAAY,EACZC,GAAG,EAiBJ;IAKC,oCAAoC;IACpCC,QAAQ,4CAA4CC,SAAS,CAACb;IAC9Dc,IAAAA,+CAA4B,EAAC;QAC3Bb;IACF;IAEA,MAAMc,aAAa,MAAMC,IAAAA,8BAAc,EAAC;QACtClB;QACA,qDAAqD;QACrDO,MAAMA,QAAQN;QACdK;QACAa,OAAO;IACT;IAEA,IAAI,CAACF,WAAWG,cAAc,IAAI,CAACd,WAAW;QAC5C,yDAAyD;QACzD,mDAAmD;QACnD,MAAM,IAAIe,MACR,CAAC,uDAAuD,EAAEpB,SAAS,CAAC;IAExE;IAEA,IAAIK,WAAW;QACb,MAAM,EAAEgB,WAAW,EAAE,GAAGL;QACxB,MAAMM,iBACJD,eAAeE,IAAAA,6BAAqB,EAACF,eACjC;YACE;gBACEpB,QAAQ;oBACNuB,YAAYH,YAAYI,QAAQ,CAACD,UAAU;oBAC3CE,SAASL,YAAYI,QAAQ,CAACC,OAAO;oBACrCC,eAAeN,YAAYI,QAAQ,CAACE,aAAa;gBACnD;gBACAC,sBAAsBP,YAAYI,QAAQ,CAACG,oBAAoB;gBAC/DC,aAAa7B;YACf;SACD,GACD,MAAM8B,IAAAA,4BAAqB,EAACd,WAAWe,YAAY,CAACC,IAAI;QAE9D,OAAO,MAAMC,IAAAA,0BAAmB,EAAC;YAC/BnC;YACAQ,MAAMN;YACNsB;YACAY,gBAAgBjC,OAAOiC,cAAc;YACrCnC;YACAW;YACAC;YACAJ;YACAC;YACAC;YACAG;YACAmB,cAAcf,WAAWe,YAAY;QACvC;IACF;IAEA,OAAO,MAAMI,IAAAA,uBAAgB,EAAC;QAC5B7B,MAAMN;QACNmB,gBAAgBH,WAAWG,cAAc;QACzCe,gBAAgBjC,OAAOiC,cAAc;QACrC/B;QACAC;IACF;AACF"}