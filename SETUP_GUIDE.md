# Ford B2B Parts Platform - Setup Guide

## Project Structure

\`\`\`
ford-b2b-parts-platform/
├── frontend/                 # Next.js React Application
│   ├── package.json
│   ├── next.config.js
│   ├── tailwind.config.ts
│   ├── .env.local
│   ├── app/
│   │   ├── layout.tsx
│   │   ├── page.tsx
│   │   ├── globals.css
│   │   ├── catalog/
│   │   ├── cart/
│   │   ├── dashboard/
│   │   ├── about/
│   │   ├── contact/
│   │   └── promotions/
│   ├── components/
│   │   ├── ui/              # shadcn/ui components
│   │   ├── site-header.tsx
│   │   ├── site-footer.tsx
│   │   ├── site-search-bar.tsx
│   │   ├── product-card.tsx
│   │   ├── quick-actions.tsx
│   │   ├── news-updates.tsx
│   │   ├── bulk-upload.tsx
│   │   └── dashboard-sidebar.tsx
│   ├── lib/
│   │   ├── utils.ts
│   │   └── api.ts
│   └── public/
│       └── images/
└── backend/                  # Express.js API Server
    ├── package.json
    ├── server.js
    ├── .env.example
    ├── .env
    ├── config/
    │   └── database.js
    ├── middleware/
    │   └── auth.js
    ├── routes/
    │   ├── auth.js
    │   ├── products.js
    │   ├── cart.js
    │   ├── orders.js
    │   ├── users.js
    │   ├── upload.js
    │   └── search.js
    ├── database/
    │   ├── schema.sql
    │   └── seed-data.sql
    └── scripts/
        └── setup-database.js
\`\`\`

## Quick Setup

### 1. Create Project Structure
\`\`\`bash
mkdir ford-b2b-parts-platform
cd ford-b2b-parts-platform
mkdir frontend backend
\`\`\`

### 2. Setup Backend
\`\`\`bash
cd backend
# Copy all backend files here
npm install
cp .env.example .env
# Edit .env with your PostgreSQL credentials
npm run setup-db
npm run dev
\`\`\`

### 3. Setup Frontend
\`\`\`bash
cd ../frontend
# Copy all frontend files here
npm install
npm run dev
\`\`\`

### 4. Access Application
- Frontend: http://localhost:3000
- Backend API: http://localhost:3001

## Test Credentials
- Admin: <EMAIL> / admin123
- Manager: <EMAIL> / admin123
- User: <EMAIL> / admin123

## Environment Variables

### Backend (.env)
\`\`\`
DB_HOST=localhost
DB_PORT=5432
DB_NAME=ford_b2b_parts
DB_USER=postgres
DB_PASSWORD=your_password
JWT_SECRET=your-secret-key
PORT=3001
FRONTEND_URL=http://localhost:3000
\`\`\`

### Frontend (.env.local)
\`\`\`
NEXT_PUBLIC_API_URL=http://localhost:3001/api
\`\`\`

## Features
- ✅ User Authentication & Authorization
- ✅ Product Catalog with FINIS Code Search
- ✅ Shopping Cart & Checkout
- ✅ Order Management
- ✅ Bulk CSV Upload
- ✅ Inventory Management
- ✅ FINIS Code Mapping (Old → New)
- ✅ Role-based Access Control
- ✅ Responsive Design
\`\`\`

Now the project is properly organized! Here's what you need to do:

1. **Create the folder structure:**
   \`\`\`bash
   mkdir ford-b2b-parts-platform
   cd ford-b2b-parts-platform
   mkdir frontend backend
   \`\`\`

2. **Copy files to correct locations:**
   - All files starting with `frontend/` go in the `frontend/` folder
   - All files starting with `backend/` go in the `backend/` folder
   - The components, pages, and other React files go in `frontend/`
   - The Express routes, database files go in `backend/`

3. **Install and run:**
   \`\`\`bash
   # Backend
   cd backend
   npm install
   cp .env.example .env
   # Edit .env with your database credentials
   npm run setup-db
   npm run dev

   # Frontend (new terminal)
   cd ../frontend
   npm install
   npm run dev
   \`\`\`

The frontend will now have the proper Next.js layout and all components will be in the right place!
