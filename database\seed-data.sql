-- Seed data for Ford B2B Parts Platform

-- Insert default categories
INSERT INTO categories (name, slug, description) VALUES
('Brakes', 'brakes', 'Brake pads, rotors, calipers, and brake system components'),
('Filters', 'filters', 'Oil filters, air filters, fuel filters, and cabin filters'),
('Engine', 'engine', 'Engine components, gaskets, belts, and engine accessories'),
('Electrical', 'electrical', 'Alternators, starters, batteries, and electrical components'),
('Transmission', 'transmission', 'Transmission parts, fluids, and related components'),
('Suspension', 'suspension', 'Shocks, struts, springs, and suspension components'),
('Cooling', 'cooling', 'Radiators, thermostats, water pumps, and cooling system parts'),
('Exhaust', 'exhaust', 'Mufflers, catalytic converters, and exhaust system components');

-- Insert brands
INSERT INTO brands (name, slug) VALUES
('Ford', 'ford'),
('Motorcraft', 'motorcraft'),
('Ford Performance', 'ford-performance');

-- Get category and brand IDs for sample products
WITH category_ids AS (
    SELECT id as brakes_id FROM categories WHERE slug = 'brakes'
    UNION ALL
    SELECT id as filters_id FROM categories WHERE slug = 'filters'
    UNION ALL
    SELECT id as engine_id FROM categories WHERE slug = 'engine'
    UNION ALL
    SELECT id as electrical_id FROM categories WHERE slug = 'electrical'
),
brand_ids AS (
    SELECT id as ford_id FROM brands WHERE slug = 'ford'
    UNION ALL
    SELECT id as motorcraft_id FROM brands WHERE slug = 'motorcraft'
)

-- Insert sample products
INSERT INTO products (name, description, finis_code, part_number, category_id, brand_id, price, is_featured) VALUES
-- Brake products
('Front Brake Pad Set', 'Premium ceramic brake pads for front axle. Fits F-150 2015-2023.', '1671234', 'FL3Z-2001-A', 
 (SELECT id FROM categories WHERE slug = 'brakes'), 
 (SELECT id FROM brands WHERE slug = 'motorcraft'), 
 129.99, true),

('Rear Brake Rotor', 'Vented brake rotor for rear axle. OEM quality replacement part.', '1671235', 'FL3Z-2C026-A',
 (SELECT id FROM categories WHERE slug = 'brakes'),
 (SELECT id FROM brands WHERE slug = 'ford'),
 89.99, false),

-- Filter products  
('Engine Oil Filter', 'High-efficiency oil filter for EcoBoost engines. Fits multiple Ford models.', '1234567', 'FL820S',
 (SELECT id FROM categories WHERE slug = 'filters'),
 (SELECT id FROM brands WHERE slug = 'motorcraft'),
 19.99, true),

('Cabin Air Filter', 'Premium cabin air filter with activated carbon. Improves air quality.', '1234568', 'FP29',
 (SELECT id FROM categories WHERE slug = 'filters'),
 (SELECT id FROM brands WHERE slug = 'motorcraft'),
 24.50, false),

('Engine Air Filter', 'High-flow air filter for improved engine performance. Fits Ranger 2019+.', '7654321', 'FA1927',
 (SELECT id FROM categories WHERE slug = 'filters'),
 (SELECT id FROM brands WHERE slug = 'motorcraft'),
 32.99, true),

-- Engine products
('Spark Plug Set', 'Iridium spark plugs for Duratec engines. Set of 4 plugs.', '2222333', 'SP515',
 (SELECT id FROM categories WHERE slug = 'engine'),
 (SELECT id FROM brands WHERE slug = 'motorcraft'),
 39.99, true),

('Timing Belt Kit', 'Complete timing belt kit with tensioner and idler pulleys.', '5556667', 'TK1001',
 (SELECT id FROM categories WHERE slug = 'engine'),
 (SELECT id FROM brands WHERE slug = 'ford'),
 199.00, false),

-- Electrical products
('Alternator', 'Remanufactured alternator, 130 amp. Fits F-150 2011-2014.', '8889990', 'GL8Z-10346-A',
 (SELECT id FROM categories WHERE slug = 'electrical'),
 (SELECT id FROM brands WHERE slug = 'ford'),
 349.00, true),

('Battery', 'Maintenance-free battery, 650 CCA. 3-year warranty.', '9991111', 'BXT65650',
 (SELECT id FROM categories WHERE slug = 'electrical'),
 (SELECT id FROM brands WHERE slug = 'motorcraft'),
 159.99, false),

('Starter Motor', 'High-torque starter motor for diesel engines. Remanufactured.', '3334445', 'BC3Z-11002-A',
 (SELECT id FROM categories WHERE slug = 'electrical'),
 (SELECT id FROM brands WHERE slug = 'ford'),
 289.99, false);

-- Insert inventory for all products
INSERT INTO inventory (product_id, quantity_on_hand, reorder_point)
SELECT id, 
       CASE 
           WHEN random() < 0.1 THEN 0  -- 10% out of stock
           ELSE floor(random() * 100 + 10)::integer  -- 10-110 units
       END,
       floor(random() * 20 + 5)::integer  -- 5-25 reorder point
FROM products;

-- Insert some FINIS mappings (old to new part numbers)
INSERT INTO finis_mappings (old_finis, new_finis, mapping_type, notes) VALUES
('1671230', '1671234', 'superseded', 'Updated brake pad compound for better performance'),
('1234560', '1234567', 'superseded', 'New filter design with improved filtration'),
('7654320', '7654321', 'equivalent', 'Same part, updated part number system'),
('2222330', '2222333', 'superseded', 'Upgraded to iridium plugs from platinum');

-- Insert sample promotions
INSERT INTO promotions (name, description, promotion_type, discount_value, start_date, end_date, applies_to, applicable_ids) VALUES
('Summer Service Special', 'Save 10% on all filters and spark plugs', 'percentage', 10.00, 
 CURRENT_DATE - INTERVAL '30 days', CURRENT_DATE + INTERVAL '60 days', 'category',
 ARRAY[(SELECT id FROM categories WHERE slug = 'filters')]),

('Brake Bundle Deal', 'Buy brake pads and rotors together, save 5%', 'percentage', 5.00,
 CURRENT_DATE - INTERVAL '15 days', CURRENT_DATE + INTERVAL '45 days', 'category',
 ARRAY[(SELECT id FROM categories WHERE slug = 'brakes')]);

-- Create a sample admin user (password: 'admin123')
INSERT INTO users (email, password_hash, first_name, last_name, role, dealership_name, dealership_code) VALUES
('<EMAIL>', '$2b$10$rQZ8kHWfQxwjQrQvQrQvQeJ8kHWfQxwjQrQvQrQvQeJ8kHWfQxwjQr', 'Admin', 'User', 'admin', 'Ford Corporate', 'CORP001'),
('<EMAIL>', '$2b$10$rQZ8kHWfQxwjQrQvQrQvQeJ8kHWfQxwjQrQvQrQvQeJ8kHWfQxwjQr', 'John', 'Dealer', 'manager', 'Downtown Ford', 'DTF001'),
('<EMAIL>', '$2b$10$rQZ8kHWfQxwjQrQvQrQvQeJ8kHWfQxwjQrQvQrQvQeJ8kHWfQxwjQr', 'Mike', 'Technician', 'user', 'Downtown Ford', 'DTF001');
