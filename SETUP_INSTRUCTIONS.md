# Setup Instructions

## Prerequisites

- Node.js 18+ installed
- PostgreSQL database running
- Git (optional)

## Step-by-Step Setup

### 1. Create Project Structure

\`\`\`bash
mkdir ford-b2b-parts-platform
cd ford-b2b-parts-platform
\`\`\`

### 2. Setup Backend

\`\`\`bash
mkdir backend
cd backend

# Copy all backend files from the code project
# - package.json
# - server.js
# - .env.example
# - config/
# - middleware/
# - routes/
# - database/
# - scripts/

# Install dependencies
npm install

# Setup environment
cp .env.example .env
# Edit .env with your PostgreSQL credentials

# Setup database
npm run setup-db

# Start backend server
npm run dev
\`\`\`

Backend will run on http://localhost:3001

### 3. Setup Frontend

\`\`\`bash
cd ../
mkdir frontend
cd frontend

# Copy all frontend files from the code project
# - package.json
# - next.config.js
# - tailwind.config.ts
# - app/
# - components/
# - lib/
# - public/

# Install dependencies
npm install

# Setup environment
echo "NEXT_PUBLIC_API_URL=http://localhost:3001/api" > .env.local

# Start frontend server
npm run dev
\`\`\`

Frontend will run on http://localhost:3000

### 4. Test the Application

1. Open http://localhost:3000 in your browser
2. Try logging in with test credentials:
   - Email: <EMAIL>
   - Password: admin123

### 5. Development Workflow

Run both servers simultaneously:

**Terminal 1 (Backend):**
\`\`\`bash
cd backend
npm run dev
\`\`\`

**Terminal 2 (Frontend):**
\`\`\`bash
cd frontend
npm run dev
\`\`\`

## Default Test Users

- **Admin**: <EMAIL> / admin123
- **Manager**: <EMAIL> / admin123  
- **User**: <EMAIL> / admin123

## Database Schema

The database includes:
- Products with FINIS codes
- Categories and brands
- User authentication
- Shopping cart functionality
- Order management
- Bulk upload history
- Inventory tracking

## API Endpoints

- `GET /api/products` - Product catalog
- `POST /api/auth/login` - User authentication
- `GET /api/cart` - Shopping cart
- `POST /api/orders` - Create orders
- `POST /api/upload/bulk` - Bulk CSV upload

## Troubleshooting

1. **Database connection issues**: Check PostgreSQL is running and credentials in .env
2. **CORS errors**: Ensure FRONTEND_URL in backend .env matches frontend URL
3. **Port conflicts**: Change PORT in .env files if needed
4. **Missing dependencies**: Run `npm install` in both directories

## Next Steps

1. Replace placeholder images with official Ford assets
2. Configure production database
3. Set up SSL certificates
4. Configure email service for notifications
5. Add monitoring and logging
