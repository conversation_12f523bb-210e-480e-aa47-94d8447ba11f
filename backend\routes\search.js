const express = require("express")
const { query } = require("express-validator")
const pool = require("../config/database")
const { authenticateToken } = require("../middleware/auth")

const router = express.Router()

// Search suggestions endpoint
router.get("/suggestions", [query("q").trim().isLength({ min: 1, max: 100 })], async (req, res) => {
  try {
    const { q } = req.query
    const searchTerm = `%${q}%`

    const query_text = `
      SELECT DISTINCT
        p.id,
        p.name,
        p.finis_code,
        p.part_number,
        c.name as category_name,
        b.name as brand_name,
        'product' as type
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      LEFT JOIN brands b ON p.brand_id = b.id
      WHERE p.is_active = true AND (
        p.name ILIKE $1 OR 
        p.finis_code ILIKE $1 OR 
        p.part_number ILIKE $1
      )
      ORDER BY 
        CASE WHEN p.finis_code ILIKE $1 THEN 1 ELSE 2 END,
        p.name
      LIMIT 8
    `

    const result = await pool.query(query_text, [searchTerm])

    const suggestions = result.rows.map((row) => ({
      id: row.id,
      label: row.name,
      finis: row.finis_code,
      partNumber: row.part_number,
      category: row.category_name,
      brand: row.brand_name,
      href: `/catalog?finis=${row.finis_code}`,
    }))

    res.json(suggestions)
  } catch (error) {
    console.error("Search suggestions error:", error)
    res.status(500).json({ error: "Failed to fetch suggestions" })
  }
})

// Log search query (optional, for analytics)
router.post(
  "/log",
  authenticateToken,
  [query("q").trim().isLength({ min: 1, max: 500 }), query("results").optional().isInt({ min: 0 })],
  async (req, res) => {
    try {
      const { q, results = 0 } = req.query

      await pool.query("INSERT INTO search_logs (user_id, search_query, results_count) VALUES ($1, $2, $3)", [
        req.user.id,
        q,
        results,
      ])

      res.json({ success: true })
    } catch (error) {
      console.error("Search log error:", error)
      res.status(500).json({ error: "Failed to log search" })
    }
  },
)

module.exports = router
