import Image from "next/image"
import SiteHeader from "@/components/site-header"
import SiteFooter from "@/components/site-footer"
import QuickActions from "@/components/quick-actions"
import ProductCard from "@/components/product-card"
import NewsUpdates from "@/components/news-updates"

const FEATURED = [
  { id: "p1", name: "Brake Pad Kit", finis: "1671234", price: 129.99, available: true },
  { id: "p2", name: "Oil Filter", finis: "1234567", price: 19.99, available: true },
  { id: "p3", name: "Air Filter", finis: "7654321", price: 24.5, available: false },
  { id: "p4", name: "Spark Plug", finis: "2222333", price: 9.99, available: true },
  { id: "p5", name: "Timing Belt", finis: "5556667", price: 199.0, available: true },
  { id: "p6", name: "Alternator", finis: "8889990", price: 349.0, available: true },
]

export default function HomePage() {
  return (
    <div className="bg-white">
      <SiteHeader />

      <main className="min-h-screen" role="main">
        {/* Hero */}
        <section aria-label="Hero" className="relative">
          <div className="relative">
            <Image
              src="/images/hero.webp"
              alt="Ford dealership parts warehouse"
              width={2400}
              height={800}
              className="w-full h-[250px] md:h-[400px] object-cover"
              priority
            />
            <div className="absolute inset-0 bg-gradient-to-r from-black/30 to-black/20" />
            <div className="absolute inset-0 flex items-center">
              <div className="mx-auto max-w-[1200px] px-4 md:px-6">
                <div className="max-w-xl">
                  <h1 className="text-white text-[32px] leading-[40px] font-bold">
                    Genuine Ford Parts for Dealerships & Service Centers
                  </h1>
                  <p className="mt-2 text-white/90 text-[14px] leading-5">
                    Streamlined procurement, real-time availability, and bulk ordering tools built for professionals.
                  </p>
                  <a
                    href="/catalog"
                    className="inline-flex mt-4 items-center justify-center rounded-md bg-[#003478] px-6 py-3 text-white hover:bg-[#002456] focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-white"
                  >
                    Find Your Parts
                  </a>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Quick actions */}
        <section className="mx-auto max-w-[1200px] px-4 md:px-6 py-8">
          <QuickActions />
        </section>

        {/* Featured products */}
        <section className="mx-auto max-w-[1200px] px-4 md:px-6 py-4" aria-labelledby="featured-heading">
          <div className="flex items-center justify-between mb-3">
            <h2 id="featured-heading" className="text-[24px] leading-8 font-semibold text-[#2D3436]">
              Featured Products
            </h2>
            <a href="/catalog" className="text-sm text-[#003478] hover:underline">
              View All
            </a>
          </div>
          <div className="grid gap-4 grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6">
            {FEATURED.map((p) => (
              <ProductCard key={p.id} product={p as any} />
            ))}
          </div>
        </section>

        {/* News */}
        <section className="mx-auto max-w-[1200px] px-4 md:px-6 py-8">
          <NewsUpdates />
        </section>
      </main>

      <SiteFooter />
    </div>
  )
}
