{"version": 3, "sources": ["../../../src/server/base-http/web.ts"], "names": ["WebNextRequest", "WebNextResponse", "BaseNextRequest", "constructor", "request", "url", "URL", "method", "href", "slice", "origin", "length", "clone", "body", "fetchMetrics", "headers", "name", "value", "entries", "parseBody", "_limit", "Error", "BaseNextResponse", "transformStream", "TransformStream", "writable", "Headers", "textBody", "undefined", "sendPromise", "Detached<PERSON>romise", "_sent", "<PERSON><PERSON><PERSON><PERSON>", "delete", "val", "Array", "isArray", "append", "removeHeader", "getHeader<PERSON><PERSON>ues", "<PERSON><PERSON><PERSON><PERSON>", "split", "map", "v", "trimStart", "get", "getHeaders", "toNodeOutgoingHttpHeaders", "<PERSON><PERSON><PERSON><PERSON>", "has", "append<PERSON><PERSON>er", "send", "resolve", "sent", "toResponse", "promise", "Response", "readable", "status", "statusCode", "statusText", "statusMessage"], "mappings": ";;;;;;;;;;;;;;;IAQaA,cAAc;eAAdA;;IA2BAC,eAAe;eAAfA;;;uBAhC6B;uBACQ;iCAClB;AAGzB,MAAMD,uBAAuBE,sBAAe;IAKjDC,YAAYC,OAAwB,CAAE;QACpC,MAAMC,MAAM,IAAIC,IAAIF,QAAQC,GAAG;QAE/B,KAAK,CACHD,QAAQG,MAAM,EACdF,IAAIG,IAAI,CAACC,KAAK,CAACJ,IAAIK,MAAM,CAACC,MAAM,GAChCP,QAAQQ,KAAK,GAAGC,IAAI;QAEtB,IAAI,CAACT,OAAO,GAAGA;QACf,IAAI,CAACU,YAAY,GAAGV,QAAQU,YAAY;QAExC,IAAI,CAACC,OAAO,GAAG,CAAC;QAChB,KAAK,MAAM,CAACC,MAAMC,MAAM,IAAIb,QAAQW,OAAO,CAACG,OAAO,GAAI;YACrD,IAAI,CAACH,OAAO,CAACC,KAAK,GAAGC;QACvB;IACF;IAEA,MAAME,UAAUC,MAAuB,EAAgB;QACrD,MAAM,IAAIC,MAAM;IAClB;AACF;AAEO,MAAMpB,wBAAwBqB,uBAAgB;IAOnDnB,YAAY,AAAOoB,kBAAkB,IAAIC,iBAAiB,CAAE;QAC1D,KAAK,CAACD,gBAAgBE,QAAQ;aADbF,kBAAAA;aANXR,UAAU,IAAIW;aACdC,WAA+BC;aAmDtBC,cAAc,IAAIC,gCAAe;aAC1CC,QAAQ;IA7ChB;IAEAC,UAAUhB,IAAY,EAAEC,KAAwB,EAAQ;QACtD,IAAI,CAACF,OAAO,CAACkB,MAAM,CAACjB;QACpB,KAAK,MAAMkB,OAAOC,MAAMC,OAAO,CAACnB,SAASA,QAAQ;YAACA;SAAM,CAAE;YACxD,IAAI,CAACF,OAAO,CAACsB,MAAM,CAACrB,MAAMkB;QAC5B;QACA,OAAO,IAAI;IACb;IAEAI,aAAatB,IAAY,EAAQ;QAC/B,IAAI,CAACD,OAAO,CAACkB,MAAM,CAACjB;QACpB,OAAO,IAAI;IACb;IAEAuB,gBAAgBvB,IAAY,EAAwB;YAE3C;QADP,iEAAiE;QACjE,QAAO,kBAAA,IAAI,CAACwB,SAAS,CAACxB,0BAAf,gBACHyB,KAAK,CAAC,KACPC,GAAG,CAAC,CAACC,IAAMA,EAAEC,SAAS;IAC3B;IAEAJ,UAAUxB,IAAY,EAAsB;QAC1C,OAAO,IAAI,CAACD,OAAO,CAAC8B,GAAG,CAAC7B,SAASY;IACnC;IAEAkB,aAAkC;QAChC,OAAOC,IAAAA,gCAAyB,EAAC,IAAI,CAAChC,OAAO;IAC/C;IAEAiC,UAAUhC,IAAY,EAAW;QAC/B,OAAO,IAAI,CAACD,OAAO,CAACkC,GAAG,CAACjC;IAC1B;IAEAkC,aAAalC,IAAY,EAAEC,KAAa,EAAQ;QAC9C,IAAI,CAACF,OAAO,CAACsB,MAAM,CAACrB,MAAMC;QAC1B,OAAO,IAAI;IACb;IAEAJ,KAAKI,KAAa,EAAE;QAClB,IAAI,CAACU,QAAQ,GAAGV;QAChB,OAAO,IAAI;IACb;IAIOkC,OAAO;QACZ,IAAI,CAACtB,WAAW,CAACuB,OAAO;QACxB,IAAI,CAACrB,KAAK,GAAG;IACf;IAEA,IAAIsB,OAAO;QACT,OAAO,IAAI,CAACtB,KAAK;IACnB;IAEA,MAAauB,aAAa;QACxB,6DAA6D;QAC7D,IAAI,CAAC,IAAI,CAACD,IAAI,EAAE,MAAM,IAAI,CAACxB,WAAW,CAAC0B,OAAO;QAE9C,OAAO,IAAIC,SAAS,IAAI,CAAC7B,QAAQ,IAAI,IAAI,CAACJ,eAAe,CAACkC,QAAQ,EAAE;YAClE1C,SAAS,IAAI,CAACA,OAAO;YACrB2C,QAAQ,IAAI,CAACC,UAAU;YACvBC,YAAY,IAAI,CAACC,aAAa;QAChC;IACF;AACF"}