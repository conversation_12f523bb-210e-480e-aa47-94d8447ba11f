const express = require("express")
const { body, param } = require("express-validator")
const pool = require("../config/database")
const { authenticateToken } = require("../middleware/auth")

const router = express.Router()

// All cart routes require authentication
router.use(authenticateToken)

// Get user's cart
router.get("/", async (req, res) => {
  try {
    const cartQuery = `
      SELECT 
        c.id as cart_id,
        ci.id as item_id,
        ci.quantity,
        ci.unit_price,
        p.id as product_id,
        p.name as product_name,
        p.finis_code,
        p.image_urls,
        (ci.quantity * ci.unit_price) as total_price,
        COALESCE(i.quantity_available, 0) as available_quantity
      FROM carts c
      LEFT JOIN cart_items ci ON c.id = ci.cart_id
      LEFT JOIN products p ON ci.product_id = p.id
      LEFT JOIN inventory i ON p.id = i.product_id
      WHERE c.user_id = $1
      ORDER BY ci.created_at DESC
    `

    const result = await pool.query(cartQuery, [req.user.id])

    if (result.rows.length === 0 || !result.rows[0].item_id) {
      return res.json({ items: [], total: 0 })
    }

    const items = result.rows.map((row) => ({
      id: row.item_id,
      productId: row.product_id,
      name: row.product_name,
      finisCode: row.finis_code,
      quantity: row.quantity,
      unitPrice: Number.parseFloat(row.unit_price),
      totalPrice: Number.parseFloat(row.total_price),
      imageUrls: row.image_urls,
      availableQuantity: row.available_quantity,
    }))

    const total = items.reduce((sum, item) => sum + item.totalPrice, 0)

    res.json({ items, total })
  } catch (error) {
    console.error("Cart fetch error:", error)
    res.status(500).json({ error: "Failed to fetch cart" })
  }
})

// Add item to cart
router.post("/items", [body("productId").isUUID(), body("quantity").isInt({ min: 1 })], async (req, res) => {
  try {
    const { productId, quantity } = req.body

    // Check if product exists and get current price
    const productQuery = `
      SELECT id, name, finis_code, price, is_active
      FROM products 
      WHERE id = $1 AND is_active = true
    `
    const productResult = await pool.query(productQuery, [productId])

    if (productResult.rows.length === 0) {
      return res.status(404).json({ error: "Product not found" })
    }

    const product = productResult.rows[0]

    // Get or create cart
    const cartResult = await pool.query("SELECT id FROM carts WHERE user_id = $1", [req.user.id])

    let cartId
    if (cartResult.rows.length === 0) {
      const newCartResult = await pool.query("INSERT INTO carts (user_id) VALUES ($1) RETURNING id", [req.user.id])
      cartId = newCartResult.rows[0].id
    } else {
      cartId = cartResult.rows[0].id
    }

    // Check if item already exists in cart
    const existingItemResult = await pool.query(
      "SELECT id, quantity FROM cart_items WHERE cart_id = $1 AND product_id = $2",
      [cartId, productId],
    )

    if (existingItemResult.rows.length > 0) {
      // Update existing item
      const newQuantity = existingItemResult.rows[0].quantity + quantity
      await pool.query("UPDATE cart_items SET quantity = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2", [
        newQuantity,
        existingItemResult.rows[0].id,
      ])
    } else {
      // Add new item
      await pool.query("INSERT INTO cart_items (cart_id, product_id, quantity, unit_price) VALUES ($1, $2, $3, $4)", [
        cartId,
        productId,
        quantity,
        product.price,
      ])
    }

    res.json({
      message: "Item added to cart successfully",
      product: {
        id: product.id,
        name: product.name,
        finisCode: product.finis_code,
        quantity,
      },
    })
  } catch (error) {
    console.error("Add to cart error:", error)
    res.status(500).json({ error: "Failed to add item to cart" })
  }
})

// Update cart item quantity
router.put("/items/:itemId", [param("itemId").isUUID(), body("quantity").isInt({ min: 1 })], async (req, res) => {
  try {
    const { itemId } = req.params
    const { quantity } = req.body

    // Verify item belongs to user's cart
    const verifyQuery = `
      SELECT ci.id 
      FROM cart_items ci
      JOIN carts c ON ci.cart_id = c.id
      WHERE ci.id = $1 AND c.user_id = $2
    `
    const verifyResult = await pool.query(verifyQuery, [itemId, req.user.id])

    if (verifyResult.rows.length === 0) {
      return res.status(404).json({ error: "Cart item not found" })
    }

    await pool.query("UPDATE cart_items SET quantity = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2", [
      quantity,
      itemId,
    ])

    res.json({ message: "Cart item updated successfully" })
  } catch (error) {
    console.error("Update cart item error:", error)
    res.status(500).json({ error: "Failed to update cart item" })
  }
})

// Remove item from cart
router.delete("/items/:itemId", [param("itemId").isUUID()], async (req, res) => {
  try {
    const { itemId } = req.params

    // Verify item belongs to user's cart
    const verifyQuery = `
      SELECT ci.id 
      FROM cart_items ci
      JOIN carts c ON ci.cart_id = c.id
      WHERE ci.id = $1 AND c.user_id = $2
    `
    const verifyResult = await pool.query(verifyQuery, [itemId, req.user.id])

    if (verifyResult.rows.length === 0) {
      return res.status(404).json({ error: "Cart item not found" })
    }

    await pool.query("DELETE FROM cart_items WHERE id = $1", [itemId])

    res.json({ message: "Item removed from cart successfully" })
  } catch (error) {
    console.error("Remove cart item error:", error)
    res.status(500).json({ error: "Failed to remove cart item" })
  }
})

// Clear entire cart
router.delete("/", async (req, res) => {
  try {
    await pool.query(
      `
      DELETE FROM cart_items 
      WHERE cart_id IN (SELECT id FROM carts WHERE user_id = $1)
    `,
      [req.user.id],
    )

    res.json({ message: "Cart cleared successfully" })
  } catch (error) {
    console.error("Clear cart error:", error)
    res.status(500).json({ error: "Failed to clear cart" })
  }
})

module.exports = router
