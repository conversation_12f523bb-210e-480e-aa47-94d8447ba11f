{"version": 3, "sources": ["../../../../src/server/lib/server-ipc/utils.ts"], "names": ["ipcForbiddenHeaders", "actionsForbiddenHeaders", "filterReqHeaders", "headers", "forbiddenHeaders", "key", "value", "Object", "entries", "includes", "Array", "isArray", "INTERNAL_HEADERS", "filterInternalHeaders", "header", "globalThis", "Symbol", "for"], "mappings": "AAAA,OAAO,MAAMA,sBAAsB;IACjC;IACA;IACA;IACA;IACA;IACA,+CAA+C;IAC/C;IACA,2IAA2I;IAC3I;CACD,CAAA;AAED,OAAO,MAAMC,0BAA0B;OAClCD;IACH;IACA;CACD,CAAA;AAED,OAAO,MAAME,mBAAmB,CAC9BC,SACAC;IAEA,kGAAkG;IAClG,+CAA+C;IAC/C,IAAID,OAAO,CAAC,iBAAiB,IAAIA,OAAO,CAAC,iBAAiB,KAAK,KAAK;QAClE,OAAOA,OAAO,CAAC,iBAAiB;IAClC;IAEA,KAAK,MAAM,CAACE,KAAKC,MAAM,IAAIC,OAAOC,OAAO,CAACL,SAAU;QAClD,IACEC,iBAAiBK,QAAQ,CAACJ,QAC1B,CAAEK,CAAAA,MAAMC,OAAO,CAACL,UAAU,OAAOA,UAAU,QAAO,GAClD;YACA,OAAOH,OAAO,CAACE,IAAI;QACrB;IACF;IACA,OAAOF;AACT,EAAC;AAED,6DAA6D;AAC7D,2CAA2C;AAC3C,MAAMS,mBAAmB;IACvB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,OAAO,MAAMC,wBAAwB,CACnCV;IAEA,IAAK,MAAMW,UAAUX,QAAS;QAC5B,IAAIS,iBAAiBH,QAAQ,CAACK,SAAS;YACrC,OAAOX,OAAO,CAACW,OAAO;QACxB;QAEA,4DAA4D;QAC5D,4DAA4D;QAC5D,yBAAyB;QACzB,IACEA,WAAW,6BACXX,OAAO,CAAC,6BAA6B,KACnC,AAACY,UAAkB,CAACC,OAAOC,GAAG,CAAC,kCAAkC,EACnE;YACA,OAAOd,OAAO,CAAC,0BAA0B;QAC3C;IACF;AACF,EAAC"}