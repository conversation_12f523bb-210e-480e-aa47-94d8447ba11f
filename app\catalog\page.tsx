"use client"

import { useMemo, useState } from "react"
import SiteHeader from "@/components/site-header"
import SiteFooter from "@/components/site-footer"
import ProductCard, { type Product } from "@/components/product-card"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Slider } from "@/components/ui/slider"
import { Badge } from "@/components/ui/badge"
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination"

const ALL_PRODUCTS: Product[] = Array.from({ length: 36 }).map((_, i) => ({
  id: `prod-${i + 1}`,
  name: ["Brake Pad Kit", "Oil Filter", "Air Filter", "Spark Plug", "Timing Belt", "Alternator"][i % 6] + ` ${i + 1}`,
  finis: String(1000000 + i),
  price: Math.round(((i * 7.23) % 350) + 10),
  available: i % 5 !== 0,
  brand: ["Ford", "Motorcraft"][i % 2],
  image: "/placeholder-o4b6o.png",
}))

const CATEGORIES = ["Brakes", "Filters", "Engine", "Electrical", "Transmission", "Suspension"]

export default function CatalogPage() {
  const [selectedCats, setSelectedCats] = useState<string[]>([])
  const [availability, setAvailability] = useState<string[]>([])
  const [brands, setBrands] = useState<string[]>([])
  const [price, setPrice] = useState<[number, number]>([0, 400])
  const [page, setPage] = useState(1)
  const perPage = 12

  const filtered = useMemo(() => {
    let items = ALL_PRODUCTS.filter((p) => p.price >= price[0] && p.price <= price[1])
    if (selectedCats.length) {
      // Demo: map names to categories loosely
      items = items.filter((p) => {
        const cat = p.name.includes("Brake")
          ? "Brakes"
          : p.name.includes("Filter")
            ? "Filters"
            : p.name.includes("Spark")
              ? "Engine"
              : p.name.includes("Timing")
                ? "Engine"
                : p.name.includes("Alternator")
                  ? "Electrical"
                  : "Suspension"
        return selectedCats.includes(cat)
      })
    }
    if (availability.length) {
      items = items.filter((p) => availability.includes(p.available ? "in" : "out"))
    }
    if (brands.length) {
      items = items.filter((p) => brands.includes(p.brand || "Ford"))
    }
    return items
  }, [selectedCats, availability, brands, price])

  const totalPages = Math.max(1, Math.ceil(filtered.length / perPage))
  const pageItems = filtered.slice((page - 1) * perPage, page * perPage)

  return (
    <div className="bg-white">
      <SiteHeader />
      <main className="mx-auto max-w-[1200px] px-4 md:px-6 py-6 grid gap-6 md:grid-cols-[260px_1fr]">
        <aside aria-label="Filters" className="md:sticky md:top-20 h-fit">
          <h1 className="text-[24px] leading-8 font-semibold text-[#2D3436] mb-3">Filters</h1>
          <Accordion type="multiple" defaultValue={["cat", "price", "avail", "brand"]}>
            <AccordionItem value="cat">
              <AccordionTrigger className="text-base">Categories</AccordionTrigger>
              <AccordionContent>
                <div className="grid gap-2">
                  {CATEGORIES.map((c) => (
                    <Label key={c} className="flex items-center gap-2 font-normal">
                      <Checkbox
                        checked={selectedCats.includes(c)}
                        onCheckedChange={() =>
                          setSelectedCats((prev) => (prev.includes(c) ? prev.filter((x) => x !== c) : [...prev, c]))
                        }
                      />
                      {c}
                    </Label>
                  ))}
                </div>
              </AccordionContent>
            </AccordionItem>

            <AccordionItem value="price">
              <AccordionTrigger className="text-base">Price Range</AccordionTrigger>
              <AccordionContent>
                <div className="px-1 py-2">
                  <Slider
                    defaultValue={[price[0], price[1]]}
                    min={0}
                    max={400}
                    step={5}
                    onValueChange={(vals) => setPrice([vals[0], vals[1]] as [number, number])}
                  />
                  <div className="mt-2 text-sm text-[#2D3436]">
                    ${price[0]} - ${price[1]}
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>

            <AccordionItem value="avail">
              <AccordionTrigger className="text-base">Availability</AccordionTrigger>
              <AccordionContent>
                <div className="grid gap-2">
                  <Label className="flex items-center gap-2 font-normal">
                    <Checkbox
                      checked={availability.includes("in")}
                      onCheckedChange={() =>
                        setAvailability((prev) =>
                          prev.includes("in") ? prev.filter((x) => x !== "in") : [...prev, "in"],
                        )
                      }
                    />
                    In stock
                  </Label>
                  <Label className="flex items-center gap-2 font-normal">
                    <Checkbox
                      checked={availability.includes("out")}
                      onCheckedChange={() =>
                        setAvailability((prev) =>
                          prev.includes("out") ? prev.filter((x) => x !== "out") : [...prev, "out"],
                        )
                      }
                    />
                    Backorder
                  </Label>
                </div>
              </AccordionContent>
            </AccordionItem>

            <AccordionItem value="brand">
              <AccordionTrigger className="text-base">Brand</AccordionTrigger>
              <AccordionContent>
                <div className="grid gap-2">
                  {["Ford", "Motorcraft"].map((b) => (
                    <Label key={b} className="flex items-center gap-2 font-normal">
                      <Checkbox
                        checked={brands.includes(b)}
                        onCheckedChange={() =>
                          setBrands((prev) => (prev.includes(b) ? prev.filter((x) => x !== b) : [...prev, b]))
                        }
                      />
                      {b}
                    </Label>
                  ))}
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </aside>

        <section aria-label="Product Grid" className="grid gap-4">
          <div className="flex items-center justify-between">
            <h1 className="text-[24px] leading-8 font-semibold text-[#2D3436]">Product Catalog</h1>
            <Badge variant="secondary" className="text-[#2D3436]">
              {filtered.length} results
            </Badge>
          </div>
          <div className="grid gap-4 grid-cols-2 md:grid-cols-3 lg:grid-cols-3">
            {pageItems.map((p) => (
              <ProductCard key={p.id} product={p} />
            ))}
          </div>

          {/* Pagination */}
          <div className="mt-2">
            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious
                    href="#"
                    onClick={(e) => {
                      e.preventDefault()
                      setPage((p) => Math.max(1, p - 1))
                    }}
                  />
                </PaginationItem>
                {Array.from({ length: totalPages })
                  .slice(0, 5)
                  .map((_, i) => {
                    const idx = i + 1
                    return (
                      <PaginationItem key={idx}>
                        <PaginationLink
                          href="#"
                          isActive={idx === page}
                          onClick={(e) => {
                            e.preventDefault()
                            setPage(idx)
                          }}
                        >
                          {idx}
                        </PaginationLink>
                      </PaginationItem>
                    )
                  })}
                <PaginationItem>
                  <PaginationNext
                    href="#"
                    onClick={(e) => {
                      e.preventDefault()
                      setPage((p) => Math.min(totalPages, p + 1))
                    }}
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        </section>
      </main>
      <SiteFooter />
    </div>
  )
}
