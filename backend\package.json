{"name": "ford-b2b-backend", "version": "1.0.0", "description": "Ford B2B Parts Platform Backend API", "main": "server.js", "scripts": {"dev": "nodemon server.js", "start": "node server.js", "setup-db": "node scripts/setup-database.js", "seed-db": "node scripts/seed-database.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"express": "^4.18.2", "pg": "^8.11.3", "bcrypt": "^5.1.1", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "morgan": "^1.10.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "csv-parser": "^3.0.0", "joi": "^17.11.0", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["ford", "parts", "b2b", "api", "express", "postgresql"], "author": "Ford Parts Platform", "license": "MIT"}