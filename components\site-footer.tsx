import Link from "next/link"

export default function SiteFooter() {
  return (
    <footer className="border-t bg-white" role="contentinfo">
      <div className="mx-auto max-w-[1200px] px-4 md:px-6 py-10 grid gap-10 md:grid-cols-3">
        <div>
          <h3 className="text-[20px] leading-7 font-semibold text-[#2D3436] mb-3">Company Info</h3>
          <ul className="text-sm text-[#636E72] space-y-1">
            <li>1 American Road, Dearborn, MI</li>
            <li>Phone: (*************</li>
            <li>Email: <EMAIL></li>
          </ul>
        </div>
        <div>
          <h3 className="text-[20px] leading-7 font-semibold text-[#2D3436] mb-3">Quick Links</h3>
          <ul className="text-sm space-y-1">
            <li>
              <Link href="/catalog" className="text-[#003478] hover:underline">
                Products
              </Link>
            </li>
            <li>
              <Link href="/orders" className="text-[#003478] hover:underline">
                Orders
              </Link>
            </li>
            <li>
              <Link href="/account" className="text-[#003478] hover:underline">
                Account
              </Link>
            </li>
          </ul>
        </div>
        <div>
          <h3 className="text-[20px] leading-7 font-semibold text-[#2D3436] mb-3">Support</h3>
          <ul className="text-sm space-y-1">
            <li>
              <Link href="/help" className="text-[#003478] hover:underline">
                Help Center
              </Link>
            </li>
            <li>
              <Link href="/contact" className="text-[#003478] hover:underline">
                Contact
              </Link>
            </li>
            <li>
              <Link href="/technical" className="text-[#003478] hover:underline">
                Technical
              </Link>
            </li>
          </ul>
        </div>
      </div>
      <div className="border-t">
        <div className="mx-auto max-w-[1200px] px-4 md:px-6 py-6 text-sm text-[#636E72]">
          {"© 2025 Ford Parts Platform. All rights reserved."}
        </div>
      </div>
    </footer>
  )
}
