{"version": 3, "sources": ["../../../../../src/server/lib/squoosh/png/squoosh_png.js"], "names": ["cleanup", "decode", "encode", "wasm", "cachedTextDecoder", "TextDecoder", "ignoreBOM", "fatal", "cachegetUint8Memory0", "getUint8Memory0", "buffer", "memory", "Uint8Array", "getStringFromWasm0", "ptr", "len", "subarray", "cachegetUint8ClampedMemory0", "getUint8ClampedMemory0", "Uint8ClampedArray", "getClampedArrayU8FromWasm0", "heap", "Array", "fill", "undefined", "push", "heap_next", "length", "addHeapObject", "obj", "idx", "WASM_VECTOR_LEN", "passArray8ToWasm0", "arg", "malloc", "set", "cachegetInt32Memory0", "getInt32Memory0", "Int32Array", "getArrayU8FromWasm0", "data", "width", "height", "retptr", "__wbindgen_add_to_stack_pointer", "ptr0", "__wbindgen_malloc", "len0", "r0", "r1", "v1", "slice", "__wbindgen_free", "getObject", "dropObject", "takeObject", "ret", "load", "module", "imports", "Response", "WebAssembly", "instantiateStreaming", "bytes", "arrayBuffer", "instantiate", "instance", "Instance", "init", "input", "wbg", "__wbg_newwithownedu8clampedarrayandsh_787b2db8ea6bfd62", "arg0", "arg1", "arg2", "arg3", "v0", "ImageData", "__wbindgen_throw", "Error", "Request", "URL", "fetch", "exports", "__wbindgen_wasm_module"], "mappings": ";;;;;;;;;;;;;;;;;IAiLgBA,OAAO;eAAPA;;IA3DAC,MAAM;eAANA;;IAwDhB,OAAmB;eAAnB;;IA3FgBC,MAAM;eAANA;;;AAnFhB,IAAIC;AAEJ,IAAIC,oBAAoB,IAAIC,YAAY,SAAS;IAC/CC,WAAW;IACXC,OAAO;AACT;AAEAH,kBAAkBH,MAAM;AAExB,IAAIO,uBAAuB;AAC3B,SAASC;IACP,IACED,yBAAyB,QACzBA,qBAAqBE,MAAM,KAAKP,KAAKQ,MAAM,CAACD,MAAM,EAClD;QACAF,uBAAuB,IAAII,WAAWT,KAAKQ,MAAM,CAACD,MAAM;IAC1D;IACA,OAAOF;AACT;AAEA,SAASK,mBAAmBC,GAAG,EAAEC,GAAG;IAClC,OAAOX,kBAAkBH,MAAM,CAACQ,kBAAkBO,QAAQ,CAACF,KAAKA,MAAMC;AACxE;AAEA,IAAIE,8BAA8B;AAClC,SAASC;IACP,IACED,gCAAgC,QAChCA,4BAA4BP,MAAM,KAAKP,KAAKQ,MAAM,CAACD,MAAM,EACzD;QACAO,8BAA8B,IAAIE,kBAAkBhB,KAAKQ,MAAM,CAACD,MAAM;IACxE;IACA,OAAOO;AACT;AAEA,SAASG,2BAA2BN,GAAG,EAAEC,GAAG;IAC1C,OAAOG,yBAAyBF,QAAQ,CAACF,MAAM,GAAGA,MAAM,IAAIC;AAC9D;AAEA,MAAMM,OAAO,IAAIC,MAAM,IAAIC,IAAI,CAACC;AAEhCH,KAAKI,IAAI,CAACD,WAAW,MAAM,MAAM;AAEjC,IAAIE,YAAYL,KAAKM,MAAM;AAE3B,SAASC,cAAcC,GAAG;IACxB,IAAIH,cAAcL,KAAKM,MAAM,EAAEN,KAAKI,IAAI,CAACJ,KAAKM,MAAM,GAAG;IACvD,MAAMG,MAAMJ;IACZA,YAAYL,IAAI,CAACS,IAAI;IAErBT,IAAI,CAACS,IAAI,GAAGD;IACZ,OAAOC;AACT;AAEA,IAAIC,kBAAkB;AAEtB,SAASC,kBAAkBC,GAAG,EAAEC,MAAM;IACpC,MAAMpB,MAAMoB,OAAOD,IAAIN,MAAM,GAAG;IAChClB,kBAAkB0B,GAAG,CAACF,KAAKnB,MAAM;IACjCiB,kBAAkBE,IAAIN,MAAM;IAC5B,OAAOb;AACT;AAEA,IAAIsB,uBAAuB;AAC3B,SAASC;IACP,IACED,yBAAyB,QACzBA,qBAAqB1B,MAAM,KAAKP,KAAKQ,MAAM,CAACD,MAAM,EAClD;QACA0B,uBAAuB,IAAIE,WAAWnC,KAAKQ,MAAM,CAACD,MAAM;IAC1D;IACA,OAAO0B;AACT;AAEA,SAASG,oBAAoBzB,GAAG,EAAEC,GAAG;IACnC,OAAON,kBAAkBO,QAAQ,CAACF,MAAM,GAAGA,MAAM,IAAIC;AACvD;AAOO,SAASb,OAAOsC,IAAI,EAAEC,KAAK,EAAEC,MAAM;IACxC,IAAI;QACF,MAAMC,SAASxC,KAAKyC,+BAA+B,CAAC,CAAC;QACrD,IAAIC,OAAOb,kBAAkBQ,MAAMrC,KAAK2C,iBAAiB;QACzD,IAAIC,OAAOhB;QACX5B,KAAKD,MAAM,CAACyC,QAAQE,MAAME,MAAMN,OAAOC;QACvC,IAAIM,KAAKX,iBAAiB,CAACM,SAAS,IAAI,EAAE;QAC1C,IAAIM,KAAKZ,iBAAiB,CAACM,SAAS,IAAI,EAAE;QAC1C,IAAIO,KAAKX,oBAAoBS,IAAIC,IAAIE,KAAK;QAC1ChD,KAAKiD,eAAe,CAACJ,IAAIC,KAAK;QAC9B,OAAOC;IACT,SAAU;QACR/C,KAAKyC,+BAA+B,CAAC;IACvC;AACF;AAEA,SAASS,UAAUvB,GAAG;IACpB,OAAOT,IAAI,CAACS,IAAI;AAClB;AAEA,SAASwB,WAAWxB,GAAG;IACrB,IAAIA,MAAM,IAAI;IACdT,IAAI,CAACS,IAAI,GAAGJ;IACZA,YAAYI;AACd;AAEA,SAASyB,WAAWzB,GAAG;IACrB,MAAM0B,MAAMH,UAAUvB;IACtBwB,WAAWxB;IACX,OAAO0B;AACT;AAKO,SAASvD,OAAOuC,IAAI;IACzB,IAAIK,OAAOb,kBAAkBQ,MAAMrC,KAAK2C,iBAAiB;IACzD,IAAIC,OAAOhB;IACX,IAAIyB,MAAMrD,KAAKF,MAAM,CAAC4C,MAAME;IAC5B,OAAOQ,WAAWC;AACpB;AAEA,eAAeC,KAAKC,OAAM,EAAEC,OAAO;IACjC,IAAI,OAAOC,aAAa,cAAcF,mBAAkBE,UAAU;QAChE,IAAI,OAAOC,YAAYC,oBAAoB,KAAK,YAAY;YAC1D,OAAO,MAAMD,YAAYC,oBAAoB,CAACJ,SAAQC;QACxD;QAEA,MAAMI,QAAQ,MAAML,QAAOM,WAAW;QACtC,OAAO,MAAMH,YAAYI,WAAW,CAACF,OAAOJ;IAC9C,OAAO;QACL,MAAMO,WAAW,MAAML,YAAYI,WAAW,CAACP,SAAQC;QAEvD,IAAIO,oBAAoBL,YAAYM,QAAQ,EAAE;YAC5C,OAAO;gBAAED;gBAAUR,QAAAA;YAAO;QAC5B,OAAO;YACL,OAAOQ;QACT;IACF;AACF;AAEA,eAAeE,KAAKC,KAAK;IACvB,MAAMV,UAAU,CAAC;IACjBA,QAAQW,GAAG,GAAG,CAAC;IACfX,QAAQW,GAAG,CAACC,sDAAsD,GAChE,SAAUC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI;QAC9B,IAAIC,KAAKxD,2BAA2BoD,MAAMC,MAAMtB,KAAK;QACrDhD,KAAKiD,eAAe,CAACoB,MAAMC,OAAO;QAClC,IAAIjB,MAAM,IAAIqB,UAAUD,IAAIF,SAAS,GAAGC,SAAS;QACjD,OAAO/C,cAAc4B;IACvB;IACFG,QAAQW,GAAG,CAACQ,gBAAgB,GAAG,SAAUN,IAAI,EAAEC,IAAI;QACjD,MAAM,IAAIM,MAAMlE,mBAAmB2D,MAAMC;IAC3C;IAEA,IACE,OAAOJ,UAAU,YAChB,OAAOW,YAAY,cAAcX,iBAAiBW,WAClD,OAAOC,QAAQ,cAAcZ,iBAAiBY,KAC/C;QACAZ,QAAQa,MAAMb;IAChB;IAEA,MAAM,EAAEH,QAAQ,EAAER,QAAAA,OAAM,EAAE,GAAG,MAAMD,KAAK,MAAMY,OAAOV;IAErDxD,OAAO+D,SAASiB,OAAO;IACvBf,KAAKgB,sBAAsB,GAAG1B;IAE9B,OAAOvD;AACT;MAEA,WAAeiE;AAGR,SAASpE;IACdG,OAAO;IACPc,8BAA8B;IAC9BT,uBAAuB;IACvB4B,uBAAuB;AACzB"}